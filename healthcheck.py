#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Docker容器健康检查脚本
用于检测应用是否正常运行
"""

import sys
import time
import requests
import logging
import json
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("healthcheck")

# 健康检查配置
HEALTH_URL = "http://localhost:5000/health"
STATUS_URL = "http://localhost:5000/api/status"
TIMEOUT = 5  # 超时时间（秒）
MAX_RETRIES = 3  # 最大重试次数


def check_basic_health():
    """
    基础健康检查
    检查/health端点是否响应
    """
    try:
        response = requests.get(HEALTH_URL, timeout=TIMEOUT)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'healthy':
                logger.info("基础健康检查通过")
                return True
            else:
                logger.error(f"健康检查失败: {data}")
                return False
        else:
            logger.error(f"健康检查HTTP错误: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        logger.error("健康检查超时")
        return False
    except requests.exceptions.ConnectionError:
        logger.error("无法连接到应用")
        return False
    except Exception as e:
        logger.error(f"健康检查异常: {str(e)}")
        return False


def check_api_status():
    """
    API状态检查
    检查API服务是否正常
    """
    try:
        response = requests.get(STATUS_URL, timeout=TIMEOUT)
        
        if response.status_code == 200:
            data = response.json()
            status = data.get('status')
            model_loaded = data.get('model_loaded', False)
            
            if status == 'running':
                logger.info(f"API状态检查通过 - 模型加载: {model_loaded}")
                return True
            else:
                logger.error(f"API状态异常: {status}")
                return False
        else:
            logger.error(f"API状态检查HTTP错误: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        logger.error("API状态检查超时")
        return False
    except requests.exceptions.ConnectionError:
        logger.error("无法连接到API")
        return False
    except Exception as e:
        logger.error(f"API状态检查异常: {str(e)}")
        return False


def check_memory_usage():
    """
    内存使用检查
    检查容器内存使用情况
    """
    try:
        import psutil
        
        # 获取内存使用情况
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        
        # 内存使用率阈值
        MEMORY_WARNING_THRESHOLD = 80
        MEMORY_CRITICAL_THRESHOLD = 95
        
        if memory_percent > MEMORY_CRITICAL_THRESHOLD:
            logger.error(f"内存使用率过高: {memory_percent:.1f}%")
            return False
        elif memory_percent > MEMORY_WARNING_THRESHOLD:
            logger.warning(f"内存使用率较高: {memory_percent:.1f}%")
            return True
        else:
            logger.info(f"内存使用率正常: {memory_percent:.1f}%")
            return True
            
    except ImportError:
        logger.warning("psutil未安装，跳过内存检查")
        return True
    except Exception as e:
        logger.error(f"内存检查异常: {str(e)}")
        return True  # 内存检查失败不影响整体健康状态


def check_disk_space():
    """
    磁盘空间检查
    检查日志目录磁盘使用情况
    """
    try:
        import shutil
        
        # 检查根目录磁盘使用情况
        total, used, free = shutil.disk_usage("/")
        
        # 转换为GB
        total_gb = total // (1024**3)
        used_gb = used // (1024**3)
        free_gb = free // (1024**3)
        
        usage_percent = (used / total) * 100
        
        # 磁盘使用率阈值
        DISK_WARNING_THRESHOLD = 80
        DISK_CRITICAL_THRESHOLD = 90
        
        if usage_percent > DISK_CRITICAL_THRESHOLD:
            logger.error(f"磁盘使用率过高: {usage_percent:.1f}% (剩余: {free_gb}GB)")
            return False
        elif usage_percent > DISK_WARNING_THRESHOLD:
            logger.warning(f"磁盘使用率较高: {usage_percent:.1f}% (剩余: {free_gb}GB)")
            return True
        else:
            logger.info(f"磁盘使用率正常: {usage_percent:.1f}% (剩余: {free_gb}GB)")
            return True
            
    except Exception as e:
        logger.error(f"磁盘检查异常: {str(e)}")
        return True  # 磁盘检查失败不影响整体健康状态


def comprehensive_health_check():
    """
    综合健康检查
    执行所有健康检查项目
    """
    logger.info("开始综合健康检查...")
    
    checks = [
        ("基础健康检查", check_basic_health),
        ("API状态检查", check_api_status),
        ("内存使用检查", check_memory_usage),
        ("磁盘空间检查", check_disk_space),
    ]
    
    results = {}
    overall_healthy = True
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results[check_name] = result
            if not result:
                overall_healthy = False
        except Exception as e:
            logger.error(f"{check_name}执行失败: {str(e)}")
            results[check_name] = False
            overall_healthy = False
    
    # 输出检查结果
    logger.info("健康检查结果:")
    for check_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {check_name}: {status}")
    
    return overall_healthy


def main():
    """主函数"""
    logger.info("Docker容器健康检查开始")
    
    # 执行健康检查，支持重试
    for attempt in range(1, MAX_RETRIES + 1):
        logger.info(f"第 {attempt} 次健康检查")
        
        if comprehensive_health_check():
            logger.info("健康检查通过")
            sys.exit(0)  # 成功退出
        else:
            logger.error(f"健康检查失败 (尝试 {attempt}/{MAX_RETRIES})")
            if attempt < MAX_RETRIES:
                logger.info("等待5秒后重试...")
                time.sleep(5)
    
    logger.error("所有健康检查尝试均失败")
    sys.exit(1)  # 失败退出


if __name__ == "__main__":
    main()
