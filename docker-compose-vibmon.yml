# 振动监测系统 Docker Compose 配置文件
#
# 🔧 部署前必须修改的参数：
# 1. image: 替换为您的实际镜像地址
# 2. MINIO_ENDPOINT: 替换为您的MinIO服务器地址
# 3. MINIO_ACCESS_KEY: 替换为您的MinIO访问密钥
# 4. MINIO_SECRET_KEY: 替换为您的MinIO密钥
#
# 🚀 快速部署：
# 1. 修改上述必需参数
# 2. 运行: docker-compose up -d
# 3. 验证: curl http://localhost:5000/api/status
#
version: '3.8'

services:
  vibmon-app:
    # 🔧 必需修改：替换为您的实际镜像地址
    image: vibmon-api:latest
    container_name: vibmon-app
    restart: unless-stopped

    # 直接在这里配置所有必需的环境变量
    environment:
      # =============================================================================
      # MinIO配置 (必需修改)
      # =============================================================================
      # MinIO服务器地址 (包含协议和端口)
      - MINIO_ENDPOINT=http://**************:9000/

      # MinIO访问凭据
      - MINIO_ACCESS_KEY=15871591512
      - MINIO_SECRET_KEY=15871591512yn

            # 存储桶配置
      - MINIO_INPUT_BUCKET=readbucket
      - MINIO_OUTPUT_BUCKET=writebucket

      # MinIO连接配置
      - MINIO_SECURE=true
      - MINIO_REGION=us-east-1

      # =============================================================================
      # 应用配置
      # =============================================================================
      # =============================================================================
      # 性能配置 (可选修改)
      # =============================================================================
      # 数据处理配置
      - MAX_SAMPLES=12000
      - DEFAULT_SAMPLING_RATE=12000
      - SUPPRESS_NUMPY_WARNINGS=true

      # 文件上传限制
      - MAX_CONTENT_LENGTH=20971520

      # MinIO超时设置
      - MINIO_TIMEOUT_SECONDS=600
      - MINIO_MAX_FILE_SIZE_MB=500
      - MINIO_MEMORY_LIMIT_MB=1024

      # JSON配置
      - JSON_AS_ASCII=false
      - JSONIFY_MIMETYPE=application/json; charset=utf-8

      # 目录配置 (容器内路径，通常不需要修改)
      - LOG_DIR=/app/logs
      - CONFIG_DIR=/app/config

    # 端口映射
    ports:
      - "5000:5000"

    # 文件挂载
    volumes:
    #   # 配置文件 (只读)
      - ./config:/app/config:rw

      # 日志目录 (读写)
      - ./logs:/app/logs:rw


    # 资源限制
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '4.0'
        reservations:
          memory: 2G
          cpus: '2'

    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
