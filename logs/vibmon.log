2025-07-15 18:03:49,361 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-15 18:03:49,665 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-15 18:03:49,666 - vibmon_app - INFO - [STARTUP] 振动监测系统启动 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:287 in main()]
2025-07-15 18:03:49,666 - vibmon_app - INFO - [CONFIG] 模式: production | 日志级别: MINIMAL [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:288 in main()]
2025-07-15 18:03:49,667 - vibmon_app - INFO - [CONFIG] 监听地址: 0.0.0.0:5000 | 调试模式: False [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:289 in main()]
2025-07-15 18:08:49,117 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-15 18:08:50,631 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-15 18:08:50,632 - vibmon_app - INFO - [STARTUP] 振动监测系统启动 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:287 in main()]
2025-07-15 18:08:50,633 - vibmon_app - INFO - [CONFIG] 模式: production | 日志级别: MINIMAL [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:288 in main()]
2025-07-15 18:08:50,633 - vibmon_app - INFO - [CONFIG] 监听地址: 0.0.0.0:5000 | 调试模式: False [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:289 in main()]
2025-07-16 09:54:43,557 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 09:54:44,339 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 09:54:44,341 - vibmon_app - INFO - [STARTUP] 振动监测系统启动 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:287 in main()]
2025-07-16 09:54:44,341 - vibmon_app - INFO - [CONFIG] 模式: production | 日志级别: MINIMAL [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:288 in main()]
2025-07-16 09:54:44,342 - vibmon_app - INFO - [CONFIG] 监听地址: 0.0.0.0:5000 | 调试模式: False [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:289 in main()]
2025-07-16 09:56:12,927 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 09:56:13,442 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 09:56:13,444 - vibmon_app - INFO - [STARTUP] 振动监测系统启动 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:287 in main()]
2025-07-16 09:56:13,445 - vibmon_app - INFO - [CONFIG] 模式: production | 日志级别: MINIMAL [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:288 in main()]
2025-07-16 09:56:13,445 - vibmon_app - INFO - [CONFIG] 监听地址: 0.0.0.0:5000 | 调试模式: False [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:289 in main()]
2025-07-16 10:00:22,749 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:00:23,100 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:00:23,102 - vibmon_app - INFO - [STARTUP] 振动监测系统启动 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:287 in main()]
2025-07-16 10:00:23,102 - vibmon_app - INFO - [CONFIG] 模式: production | 日志级别: MINIMAL [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:288 in main()]
2025-07-16 10:00:23,103 - vibmon_app - INFO - [CONFIG] 监听地址: 0.0.0.0:5000 | 调试模式: False [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:289 in main()]
2025-07-16 10:02:05,390 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:02:05,727 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:02:05,728 - vibmon_app - INFO - [STARTUP] 振动监测系统启动 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:287 in main()]
2025-07-16 10:02:05,728 - vibmon_app - INFO - [CONFIG] 模式: production | 日志级别: MINIMAL [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:288 in main()]
2025-07-16 10:02:05,729 - vibmon_app - INFO - [CONFIG] 监听地址: 0.0.0.0:5000 | 调试模式: False [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:289 in main()]
2025-07-16 10:10:52,618 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:10:53,008 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:10:53,009 - vibmon_app - INFO - [STARTUP] 振动监测系统启动 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:287 in main()]
2025-07-16 10:10:53,010 - vibmon_app - INFO - [CONFIG] 模式: production | 日志级别: MINIMAL [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:288 in main()]
2025-07-16 10:10:53,010 - vibmon_app - INFO - [CONFIG] 监听地址: 0.0.0.0:5000 | 调试模式: False [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:289 in main()]
2025-07-16 10:11:12,558 - vibmon_app - INFO - [API_REQUEST] GET api.get_status | RequestID: 1752631872557124 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:11:12,559 - vibmon_app - INFO - [API_CALL] status | Method: GET | IP: 127.0.0.1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:37 in get_status()]
2025-07-16 10:11:12,560 - vibmon_app - INFO - [API_RESPONSE] GET api.get_status | RequestID: 1752631872557124 | Status: 200 | Duration: 3.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:11:41,312 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631901312832 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "csv_data/0000.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:11:41,313 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'csv_data/0000.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:67 in get_freq_am()]
2025-07-16 10:11:41,314 - vibmon_app - INFO - [PARAMS] 频率幅值计算 | RequestID: 1752631901312832 | 关键参数: {"equipment_code": "TEST001", "data_path": "csv_data/0000.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:11:41,315 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752631901312832 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:11:41,393 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752631901312832 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:11:41,395 - vibmon_app - INFO - [PROCESSING] 开始包络谱计算 | RequestID: 1752631901312832 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:11:41,403 - vibmon_app - INFO - [PROCESSING] 包络谱计算完成 | RequestID: 1752631901312832 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:11:41,404 - vibmon_app - INFO - [RESULT] 频率幅值计算完成 | RequestID: 1752631901312832 | 结果摘要: {"status": "success", "key_metrics": {}} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:258 in log_result_summary()]
2025-07-16 10:11:41,421 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631901312832 | Status: 200 | Duration: 108.54ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,220 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,224 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,233 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,239 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,239 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,241 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,241 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,245 - vibmon_app - INFO - [API_REQUEST] GET api.get_status | RequestID: 1752631931243232 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,245 - vibmon_app - INFO - [API_REQUEST] GET api.get_status | RequestID: 1752631931243232 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,245 - vibmon_app - INFO - [API_CALL] status | Method: GET | IP: 127.0.0.1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:37 in get_status()]
2025-07-16 10:12:11,245 - vibmon_app - INFO - [API_CALL] status | Method: GET | IP: 127.0.0.1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:37 in get_status()]
2025-07-16 10:12:11,246 - vibmon_app - INFO - [API_RESPONSE] GET api.get_status | RequestID: 1752631931243232 | Status: 200 | Duration: 3.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,246 - vibmon_app - INFO - [API_RESPONSE] GET api.get_status | RequestID: 1752631931243232 | Status: 200 | Duration: 3.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,249 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,249 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,256 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,256 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,256 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,258 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,258 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,258 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,311 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931311462 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpfk_8o26l.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,311 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931311462 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpfk_8o26l.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,311 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931311462 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpfk_8o26l.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,312 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpfk_8o26l.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:67 in get_freq_am()]
2025-07-16 10:12:11,312 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpfk_8o26l.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:67 in get_freq_am()]
2025-07-16 10:12:11,312 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpfk_8o26l.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:67 in get_freq_am()]
2025-07-16 10:12:11,313 - vibmon_app - INFO - [PARAMS] 频率幅值计算 | RequestID: 1752631931311462 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpfk_8o26l.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:12:11,313 - vibmon_app - INFO - [PARAMS] 频率幅值计算 | RequestID: 1752631931311462 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpfk_8o26l.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:12:11,313 - vibmon_app - INFO - [PARAMS] 频率幅值计算 | RequestID: 1752631931311462 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpfk_8o26l.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:12:11,313 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752631931311462 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,313 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752631931311462 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,313 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752631931311462 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,330 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752631931311462 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,330 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752631931311462 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,330 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752631931311462 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,331 - vibmon_app - INFO - [PROCESSING] 开始包络谱计算 | RequestID: 1752631931311462 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,331 - vibmon_app - INFO - [PROCESSING] 开始包络谱计算 | RequestID: 1752631931311462 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,331 - vibmon_app - INFO - [PROCESSING] 开始包络谱计算 | RequestID: 1752631931311462 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,334 - vibmon_app - INFO - [PROCESSING] 包络谱计算完成 | RequestID: 1752631931311462 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,334 - vibmon_app - INFO - [PROCESSING] 包络谱计算完成 | RequestID: 1752631931311462 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,334 - vibmon_app - INFO - [PROCESSING] 包络谱计算完成 | RequestID: 1752631931311462 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,335 - vibmon_app - INFO - [RESULT] 频率幅值计算完成 | RequestID: 1752631931311462 | 结果摘要: {"status": "success", "key_metrics": {}} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:258 in log_result_summary()]
2025-07-16 10:12:11,335 - vibmon_app - INFO - [RESULT] 频率幅值计算完成 | RequestID: 1752631931311462 | 结果摘要: {"status": "success", "key_metrics": {}} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:258 in log_result_summary()]
2025-07-16 10:12:11,335 - vibmon_app - INFO - [RESULT] 频率幅值计算完成 | RequestID: 1752631931311462 | 结果摘要: {"status": "success", "key_metrics": {}} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:258 in log_result_summary()]
2025-07-16 10:12:11,367 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931311462 | Status: 200 | Duration: 55.72ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,367 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931311462 | Status: 200 | Duration: 55.72ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,367 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931311462 | Status: 200 | Duration: 55.72ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,382 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,382 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,382 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,389 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,389 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,389 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,389 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,391 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,391 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,391 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,391 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,393 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931393169 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,393 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931393169 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,393 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931393169 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,393 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931393169 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,394 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:67 in get_freq_am()]
2025-07-16 10:12:11,394 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:67 in get_freq_am()]
2025-07-16 10:12:11,394 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:67 in get_freq_am()]
2025-07-16 10:12:11,394 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:67 in get_freq_am()]
2025-07-16 10:12:11,395 - vibmon_app - WARNING - 频率幅值数据验证错误: 缺少必要参数: data_path [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:173 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 91, in get_freq_am
    validated_data = RequestValidator.validate_freq_am_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 40, in validate_freq_am_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: data_path
2025-07-16 10:12:11,395 - vibmon_app - WARNING - 频率幅值数据验证错误: 缺少必要参数: data_path [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:173 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 91, in get_freq_am
    validated_data = RequestValidator.validate_freq_am_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 40, in validate_freq_am_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: data_path
2025-07-16 10:12:11,395 - vibmon_app - WARNING - 频率幅值数据验证错误: 缺少必要参数: data_path [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:173 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 91, in get_freq_am
    validated_data = RequestValidator.validate_freq_am_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 40, in validate_freq_am_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: data_path
2025-07-16 10:12:11,395 - vibmon_app - WARNING - 频率幅值数据验证错误: 缺少必要参数: data_path [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:173 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 91, in get_freq_am
    validated_data = RequestValidator.validate_freq_am_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 40, in validate_freq_am_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: data_path
2025-07-16 10:12:11,396 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931393169 | Status: 400 | Duration: 3.02ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,396 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931393169 | Status: 400 | Duration: 3.02ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,396 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931393169 | Status: 400 | Duration: 3.02ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,396 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931393169 | Status: 400 | Duration: 3.02ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,399 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,399 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,399 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,399 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,407 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,407 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,407 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,407 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,407 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,409 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,409 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,409 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,409 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,409 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,411 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931411170 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "nonexistent_file.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,411 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931411170 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "nonexistent_file.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,411 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931411170 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "nonexistent_file.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,411 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931411170 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "nonexistent_file.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,411 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931411170 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "nonexistent_file.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,412 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'nonexistent_file.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:67 in get_freq_am()]
2025-07-16 10:12:11,412 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'nonexistent_file.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:67 in get_freq_am()]
2025-07-16 10:12:11,412 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'nonexistent_file.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:67 in get_freq_am()]
2025-07-16 10:12:11,412 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'nonexistent_file.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:67 in get_freq_am()]
2025-07-16 10:12:11,412 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'nonexistent_file.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:67 in get_freq_am()]
2025-07-16 10:12:11,413 - vibmon_app - WARNING - 频率幅值请求参数错误: 文件不存在: nonexistent_file.csv [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:165 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 91, in get_freq_am
    validated_data = RequestValidator.validate_freq_am_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 54, in validate_freq_am_request
    check_file_exists(data_path)
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\helpers.py", line 82, in check_file_exists
    raise FileNotFoundError(f"文件不存在: {file_path}")
FileNotFoundError: 文件不存在: nonexistent_file.csv
2025-07-16 10:12:11,413 - vibmon_app - WARNING - 频率幅值请求参数错误: 文件不存在: nonexistent_file.csv [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:165 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 91, in get_freq_am
    validated_data = RequestValidator.validate_freq_am_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 54, in validate_freq_am_request
    check_file_exists(data_path)
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\helpers.py", line 82, in check_file_exists
    raise FileNotFoundError(f"文件不存在: {file_path}")
FileNotFoundError: 文件不存在: nonexistent_file.csv
2025-07-16 10:12:11,413 - vibmon_app - WARNING - 频率幅值请求参数错误: 文件不存在: nonexistent_file.csv [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:165 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 91, in get_freq_am
    validated_data = RequestValidator.validate_freq_am_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 54, in validate_freq_am_request
    check_file_exists(data_path)
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\helpers.py", line 82, in check_file_exists
    raise FileNotFoundError(f"文件不存在: {file_path}")
FileNotFoundError: 文件不存在: nonexistent_file.csv
2025-07-16 10:12:11,413 - vibmon_app - WARNING - 频率幅值请求参数错误: 文件不存在: nonexistent_file.csv [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:165 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 91, in get_freq_am
    validated_data = RequestValidator.validate_freq_am_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 54, in validate_freq_am_request
    check_file_exists(data_path)
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\helpers.py", line 82, in check_file_exists
    raise FileNotFoundError(f"文件不存在: {file_path}")
FileNotFoundError: 文件不存在: nonexistent_file.csv
2025-07-16 10:12:11,413 - vibmon_app - WARNING - 频率幅值请求参数错误: 文件不存在: nonexistent_file.csv [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:165 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 91, in get_freq_am
    validated_data = RequestValidator.validate_freq_am_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 54, in validate_freq_am_request
    check_file_exists(data_path)
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\helpers.py", line 82, in check_file_exists
    raise FileNotFoundError(f"文件不存在: {file_path}")
FileNotFoundError: 文件不存在: nonexistent_file.csv
2025-07-16 10:12:11,414 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931411170 | Status: 400 | Duration: 3.01ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,414 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931411170 | Status: 400 | Duration: 3.01ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,414 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931411170 | Status: 400 | Duration: 3.01ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,414 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931411170 | Status: 400 | Duration: 3.01ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,414 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931411170 | Status: 400 | Duration: 3.01ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,418 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,418 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,418 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,418 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,418 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,426 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,426 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,426 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,426 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,426 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,426 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,429 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,429 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,429 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,429 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,429 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,429 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,432 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931432981 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp5e16c_hj.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,432 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931432981 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp5e16c_hj.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,432 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931432981 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp5e16c_hj.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,432 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931432981 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp5e16c_hj.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,432 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931432981 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp5e16c_hj.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,432 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931432981 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp5e16c_hj.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,433 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp5e16c_hj.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:67 in get_freq_am()]
2025-07-16 10:12:11,433 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp5e16c_hj.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:67 in get_freq_am()]
2025-07-16 10:12:11,433 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp5e16c_hj.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:67 in get_freq_am()]
2025-07-16 10:12:11,433 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp5e16c_hj.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:67 in get_freq_am()]
2025-07-16 10:12:11,433 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp5e16c_hj.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:67 in get_freq_am()]
2025-07-16 10:12:11,433 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp5e16c_hj.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:67 in get_freq_am()]
2025-07-16 10:12:11,434 - vibmon_app - INFO - [PARAMS] 频率幅值计算 | RequestID: 1752631931432981 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp5e16c_hj.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:12:11,434 - vibmon_app - INFO - [PARAMS] 频率幅值计算 | RequestID: 1752631931432981 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp5e16c_hj.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:12:11,434 - vibmon_app - INFO - [PARAMS] 频率幅值计算 | RequestID: 1752631931432981 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp5e16c_hj.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:12:11,434 - vibmon_app - INFO - [PARAMS] 频率幅值计算 | RequestID: 1752631931432981 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp5e16c_hj.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:12:11,434 - vibmon_app - INFO - [PARAMS] 频率幅值计算 | RequestID: 1752631931432981 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp5e16c_hj.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:12:11,434 - vibmon_app - INFO - [PARAMS] 频率幅值计算 | RequestID: 1752631931432981 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp5e16c_hj.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:12:11,435 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752631931432981 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,435 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752631931432981 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,435 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752631931432981 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,435 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752631931432981 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,435 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752631931432981 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,435 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752631931432981 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,443 - vibmon_app - WARNING - 频率幅值请求参数错误: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2'] [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:165 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 98, in get_freq_am
    data_h, data_a, data_v, sampling_rate = RequestValidator.load_and_validate_csv(
                                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 157, in load_and_validate_csv
    validate_csv_columns(csv_data, required_columns)
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\helpers.py", line 103, in validate_csv_columns
    raise ValueError(
ValueError: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2']
2025-07-16 10:12:11,443 - vibmon_app - WARNING - 频率幅值请求参数错误: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2'] [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:165 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 98, in get_freq_am
    data_h, data_a, data_v, sampling_rate = RequestValidator.load_and_validate_csv(
                                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 157, in load_and_validate_csv
    validate_csv_columns(csv_data, required_columns)
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\helpers.py", line 103, in validate_csv_columns
    raise ValueError(
ValueError: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2']
2025-07-16 10:12:11,443 - vibmon_app - WARNING - 频率幅值请求参数错误: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2'] [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:165 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 98, in get_freq_am
    data_h, data_a, data_v, sampling_rate = RequestValidator.load_and_validate_csv(
                                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 157, in load_and_validate_csv
    validate_csv_columns(csv_data, required_columns)
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\helpers.py", line 103, in validate_csv_columns
    raise ValueError(
ValueError: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2']
2025-07-16 10:12:11,443 - vibmon_app - WARNING - 频率幅值请求参数错误: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2'] [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:165 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 98, in get_freq_am
    data_h, data_a, data_v, sampling_rate = RequestValidator.load_and_validate_csv(
                                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 157, in load_and_validate_csv
    validate_csv_columns(csv_data, required_columns)
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\helpers.py", line 103, in validate_csv_columns
    raise ValueError(
ValueError: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2']
2025-07-16 10:12:11,443 - vibmon_app - WARNING - 频率幅值请求参数错误: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2'] [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:165 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 98, in get_freq_am
    data_h, data_a, data_v, sampling_rate = RequestValidator.load_and_validate_csv(
                                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 157, in load_and_validate_csv
    validate_csv_columns(csv_data, required_columns)
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\helpers.py", line 103, in validate_csv_columns
    raise ValueError(
ValueError: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2']
2025-07-16 10:12:11,443 - vibmon_app - WARNING - 频率幅值请求参数错误: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2'] [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:165 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 98, in get_freq_am
    data_h, data_a, data_v, sampling_rate = RequestValidator.load_and_validate_csv(
                                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 157, in load_and_validate_csv
    validate_csv_columns(csv_data, required_columns)
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\helpers.py", line 103, in validate_csv_columns
    raise ValueError(
ValueError: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2']
2025-07-16 10:12:11,445 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931432981 | Status: 400 | Duration: 13.01ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,445 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931432981 | Status: 400 | Duration: 13.01ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,445 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931432981 | Status: 400 | Duration: 13.01ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,445 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931432981 | Status: 400 | Duration: 13.01ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,445 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931432981 | Status: 400 | Duration: 13.01ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,445 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931432981 | Status: 400 | Duration: 13.01ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,450 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,450 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,450 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,450 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,450 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,450 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,457 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,457 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,457 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,457 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,457 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,457 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,457 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,459 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,459 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,459 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,459 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,459 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,459 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,459 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,504 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931504314 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpd_c69ca4.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,504 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931504314 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpd_c69ca4.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,504 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931504314 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpd_c69ca4.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,504 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931504314 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpd_c69ca4.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,504 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931504314 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpd_c69ca4.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,504 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931504314 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpd_c69ca4.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,504 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931504314 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpd_c69ca4.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,506 - vibmon_app - INFO - [PARAMS] 多特征计算 | RequestID: 1752631931504314 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpd_c69ca4.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:12:11,506 - vibmon_app - INFO - [PARAMS] 多特征计算 | RequestID: 1752631931504314 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpd_c69ca4.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:12:11,506 - vibmon_app - INFO - [PARAMS] 多特征计算 | RequestID: 1752631931504314 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpd_c69ca4.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:12:11,506 - vibmon_app - INFO - [PARAMS] 多特征计算 | RequestID: 1752631931504314 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpd_c69ca4.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:12:11,506 - vibmon_app - INFO - [PARAMS] 多特征计算 | RequestID: 1752631931504314 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpd_c69ca4.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:12:11,506 - vibmon_app - INFO - [PARAMS] 多特征计算 | RequestID: 1752631931504314 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpd_c69ca4.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:12:11,506 - vibmon_app - INFO - [PARAMS] 多特征计算 | RequestID: 1752631931504314 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpd_c69ca4.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:12:11,507 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,507 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,507 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,507 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,507 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,507 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,507 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,523 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,523 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,523 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,523 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,523 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,523 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,523 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,525 - vibmon_app - INFO - [PROCESSING] 开始特征提取 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,525 - vibmon_app - INFO - [PROCESSING] 开始特征提取 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,525 - vibmon_app - INFO - [PROCESSING] 开始特征提取 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,525 - vibmon_app - INFO - [PROCESSING] 开始特征提取 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,525 - vibmon_app - INFO - [PROCESSING] 开始特征提取 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,525 - vibmon_app - INFO - [PROCESSING] 开始特征提取 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,525 - vibmon_app - INFO - [PROCESSING] 开始特征提取 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,529 - vibmon_app - INFO - [PROCESSING] 特征提取完成 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,529 - vibmon_app - INFO - [PROCESSING] 特征提取完成 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,529 - vibmon_app - INFO - [PROCESSING] 特征提取完成 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,529 - vibmon_app - INFO - [PROCESSING] 特征提取完成 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,529 - vibmon_app - INFO - [PROCESSING] 特征提取完成 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,529 - vibmon_app - INFO - [PROCESSING] 特征提取完成 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,529 - vibmon_app - INFO - [PROCESSING] 特征提取完成 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,531 - vibmon_app - INFO - [PROCESSING] 开始健康度评估 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,531 - vibmon_app - INFO - [PROCESSING] 开始健康度评估 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,531 - vibmon_app - INFO - [PROCESSING] 开始健康度评估 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,531 - vibmon_app - INFO - [PROCESSING] 开始健康度评估 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,531 - vibmon_app - INFO - [PROCESSING] 开始健康度评估 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,531 - vibmon_app - INFO - [PROCESSING] 开始健康度评估 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,531 - vibmon_app - INFO - [PROCESSING] 开始健康度评估 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,533 - vibmon_app - INFO - [PROCESSING] 健康度评估完成 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,533 - vibmon_app - INFO - [PROCESSING] 健康度评估完成 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,533 - vibmon_app - INFO - [PROCESSING] 健康度评估完成 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,533 - vibmon_app - INFO - [PROCESSING] 健康度评估完成 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,533 - vibmon_app - INFO - [PROCESSING] 健康度评估完成 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,533 - vibmon_app - INFO - [PROCESSING] 健康度评估完成 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,533 - vibmon_app - INFO - [PROCESSING] 健康度评估完成 | RequestID: 1752631931504314 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:12:11,534 - vibmon_app - INFO - [RESULT] 多特征计算完成 | RequestID: 1752631931504314 | 结果摘要: {"status": "success", "key_metrics": {"health_degree": 100.0, "rms_values_total": 1.0748797336275226, "total_amplitude": 0.6210548299930087}} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:258 in log_result_summary()]
2025-07-16 10:12:11,534 - vibmon_app - INFO - [RESULT] 多特征计算完成 | RequestID: 1752631931504314 | 结果摘要: {"status": "success", "key_metrics": {"health_degree": 100.0, "rms_values_total": 1.0748797336275226, "total_amplitude": 0.6210548299930087}} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:258 in log_result_summary()]
2025-07-16 10:12:11,534 - vibmon_app - INFO - [RESULT] 多特征计算完成 | RequestID: 1752631931504314 | 结果摘要: {"status": "success", "key_metrics": {"health_degree": 100.0, "rms_values_total": 1.0748797336275226, "total_amplitude": 0.6210548299930087}} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:258 in log_result_summary()]
2025-07-16 10:12:11,534 - vibmon_app - INFO - [RESULT] 多特征计算完成 | RequestID: 1752631931504314 | 结果摘要: {"status": "success", "key_metrics": {"health_degree": 100.0, "rms_values_total": 1.0748797336275226, "total_amplitude": 0.6210548299930087}} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:258 in log_result_summary()]
2025-07-16 10:12:11,534 - vibmon_app - INFO - [RESULT] 多特征计算完成 | RequestID: 1752631931504314 | 结果摘要: {"status": "success", "key_metrics": {"health_degree": 100.0, "rms_values_total": 1.0748797336275226, "total_amplitude": 0.6210548299930087}} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:258 in log_result_summary()]
2025-07-16 10:12:11,534 - vibmon_app - INFO - [RESULT] 多特征计算完成 | RequestID: 1752631931504314 | 结果摘要: {"status": "success", "key_metrics": {"health_degree": 100.0, "rms_values_total": 1.0748797336275226, "total_amplitude": 0.6210548299930087}} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:258 in log_result_summary()]
2025-07-16 10:12:11,534 - vibmon_app - INFO - [RESULT] 多特征计算完成 | RequestID: 1752631931504314 | 结果摘要: {"status": "success", "key_metrics": {"health_degree": 100.0, "rms_values_total": 1.0748797336275226, "total_amplitude": 0.6210548299930087}} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:258 in log_result_summary()]
2025-07-16 10:12:11,535 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931504314 | Status: 200 | Duration: 30.75ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,535 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931504314 | Status: 200 | Duration: 30.75ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,535 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931504314 | Status: 200 | Duration: 30.75ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,535 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931504314 | Status: 200 | Duration: 30.75ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,535 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931504314 | Status: 200 | Duration: 30.75ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,535 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931504314 | Status: 200 | Duration: 30.75ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,535 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931504314 | Status: 200 | Duration: 30.75ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,540 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,540 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,540 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,540 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,540 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,540 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,540 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,548 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,548 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,548 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,548 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,548 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,548 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,548 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,548 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,550 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,550 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,550 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,550 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,550 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,550 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,550 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,550 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,553 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931552058 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "test.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,553 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931552058 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "test.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,553 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931552058 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "test.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,553 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931552058 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "test.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,553 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931552058 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "test.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,553 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931552058 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "test.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,553 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931552058 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "test.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,553 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931552058 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "test.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,554 - vibmon_app - WARNING - 多特征数据验证错误: 缺少必要参数: health_degree_flag [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:364 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 224, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 83, in validate_multi_features_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: health_degree_flag
2025-07-16 10:12:11,554 - vibmon_app - WARNING - 多特征数据验证错误: 缺少必要参数: health_degree_flag [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:364 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 224, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 83, in validate_multi_features_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: health_degree_flag
2025-07-16 10:12:11,554 - vibmon_app - WARNING - 多特征数据验证错误: 缺少必要参数: health_degree_flag [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:364 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 224, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 83, in validate_multi_features_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: health_degree_flag
2025-07-16 10:12:11,554 - vibmon_app - WARNING - 多特征数据验证错误: 缺少必要参数: health_degree_flag [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:364 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 224, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 83, in validate_multi_features_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: health_degree_flag
2025-07-16 10:12:11,554 - vibmon_app - WARNING - 多特征数据验证错误: 缺少必要参数: health_degree_flag [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:364 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 224, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 83, in validate_multi_features_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: health_degree_flag
2025-07-16 10:12:11,554 - vibmon_app - WARNING - 多特征数据验证错误: 缺少必要参数: health_degree_flag [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:364 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 224, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 83, in validate_multi_features_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: health_degree_flag
2025-07-16 10:12:11,554 - vibmon_app - WARNING - 多特征数据验证错误: 缺少必要参数: health_degree_flag [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:364 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 224, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 83, in validate_multi_features_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: health_degree_flag
2025-07-16 10:12:11,554 - vibmon_app - WARNING - 多特征数据验证错误: 缺少必要参数: health_degree_flag [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:364 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 224, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 83, in validate_multi_features_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: health_degree_flag
2025-07-16 10:12:11,556 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931552058 | Status: 400 | Duration: 4.01ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,556 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931552058 | Status: 400 | Duration: 4.01ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,556 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931552058 | Status: 400 | Duration: 4.01ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,556 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931552058 | Status: 400 | Duration: 4.01ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,556 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931552058 | Status: 400 | Duration: 4.01ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,556 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931552058 | Status: 400 | Duration: 4.01ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,556 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931552058 | Status: 400 | Duration: 4.01ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,556 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931552058 | Status: 400 | Duration: 4.01ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,562 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,562 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,562 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,562 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,562 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,562 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,562 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,562 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,570 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,570 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,570 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,570 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,570 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,570 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,570 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,570 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,570 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,572 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,572 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,572 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,572 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,572 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,572 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,572 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,572 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,572 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,618 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931618174 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzwg_dhby.csv", "predict_flag": 0, "health_degree_flag": 2} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,618 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931618174 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzwg_dhby.csv", "predict_flag": 0, "health_degree_flag": 2} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,618 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931618174 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzwg_dhby.csv", "predict_flag": 0, "health_degree_flag": 2} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,618 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931618174 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzwg_dhby.csv", "predict_flag": 0, "health_degree_flag": 2} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,618 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931618174 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzwg_dhby.csv", "predict_flag": 0, "health_degree_flag": 2} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,618 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931618174 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzwg_dhby.csv", "predict_flag": 0, "health_degree_flag": 2} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,618 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931618174 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzwg_dhby.csv", "predict_flag": 0, "health_degree_flag": 2} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,618 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931618174 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzwg_dhby.csv", "predict_flag": 0, "health_degree_flag": 2} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,618 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752631931618174 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzwg_dhby.csv", "predict_flag": 0, "health_degree_flag": 2} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,620 - vibmon_app - WARNING - 多特征数据验证错误: health_degree_flag必须是0或1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:364 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 224, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 110, in validate_multi_features_request
    raise InvalidDataError("health_degree_flag必须是0或1")
vibmon_app.utils.exceptions.InvalidDataError: health_degree_flag必须是0或1
2025-07-16 10:12:11,620 - vibmon_app - WARNING - 多特征数据验证错误: health_degree_flag必须是0或1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:364 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 224, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 110, in validate_multi_features_request
    raise InvalidDataError("health_degree_flag必须是0或1")
vibmon_app.utils.exceptions.InvalidDataError: health_degree_flag必须是0或1
2025-07-16 10:12:11,620 - vibmon_app - WARNING - 多特征数据验证错误: health_degree_flag必须是0或1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:364 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 224, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 110, in validate_multi_features_request
    raise InvalidDataError("health_degree_flag必须是0或1")
vibmon_app.utils.exceptions.InvalidDataError: health_degree_flag必须是0或1
2025-07-16 10:12:11,620 - vibmon_app - WARNING - 多特征数据验证错误: health_degree_flag必须是0或1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:364 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 224, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 110, in validate_multi_features_request
    raise InvalidDataError("health_degree_flag必须是0或1")
vibmon_app.utils.exceptions.InvalidDataError: health_degree_flag必须是0或1
2025-07-16 10:12:11,620 - vibmon_app - WARNING - 多特征数据验证错误: health_degree_flag必须是0或1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:364 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 224, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 110, in validate_multi_features_request
    raise InvalidDataError("health_degree_flag必须是0或1")
vibmon_app.utils.exceptions.InvalidDataError: health_degree_flag必须是0或1
2025-07-16 10:12:11,620 - vibmon_app - WARNING - 多特征数据验证错误: health_degree_flag必须是0或1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:364 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 224, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 110, in validate_multi_features_request
    raise InvalidDataError("health_degree_flag必须是0或1")
vibmon_app.utils.exceptions.InvalidDataError: health_degree_flag必须是0或1
2025-07-16 10:12:11,620 - vibmon_app - WARNING - 多特征数据验证错误: health_degree_flag必须是0或1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:364 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 224, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 110, in validate_multi_features_request
    raise InvalidDataError("health_degree_flag必须是0或1")
vibmon_app.utils.exceptions.InvalidDataError: health_degree_flag必须是0或1
2025-07-16 10:12:11,620 - vibmon_app - WARNING - 多特征数据验证错误: health_degree_flag必须是0或1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:364 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 224, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 110, in validate_multi_features_request
    raise InvalidDataError("health_degree_flag必须是0或1")
vibmon_app.utils.exceptions.InvalidDataError: health_degree_flag必须是0或1
2025-07-16 10:12:11,620 - vibmon_app - WARNING - 多特征数据验证错误: health_degree_flag必须是0或1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:364 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 224, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 110, in validate_multi_features_request
    raise InvalidDataError("health_degree_flag必须是0或1")
vibmon_app.utils.exceptions.InvalidDataError: health_degree_flag必须是0或1
2025-07-16 10:12:11,623 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931618174 | Status: 400 | Duration: 4.98ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,623 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931618174 | Status: 400 | Duration: 4.98ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,623 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931618174 | Status: 400 | Duration: 4.98ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,623 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931618174 | Status: 400 | Duration: 4.98ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,623 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931618174 | Status: 400 | Duration: 4.98ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,623 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931618174 | Status: 400 | Duration: 4.98ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,623 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931618174 | Status: 400 | Duration: 4.98ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,623 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931618174 | Status: 400 | Duration: 4.98ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,623 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752631931618174 | Status: 400 | Duration: 4.98ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,628 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,628 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,628 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,628 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,628 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,628 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,628 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,628 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,628 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:11,636 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,636 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,636 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,636 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,636 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,636 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,636 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,636 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,636 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,636 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:11,639 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,639 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,639 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,639 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,639 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,639 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,639 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,639 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,639 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,639 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:11,642 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931642830 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,642 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931642830 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,642 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931642830 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,642 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931642830 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,642 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931642830 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,642 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931642830 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,642 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931642830 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,642 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931642830 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,642 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931642830 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,642 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631931642830 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:11,644 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:11,644 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:11,644 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:11,644 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:11,644 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:11,644 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:11,644 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:11,644 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:11,644 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:11,644 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:11,697 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931642830 | Status: 500 | Duration: 54.64ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,697 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931642830 | Status: 500 | Duration: 54.64ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,697 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931642830 | Status: 500 | Duration: 54.64ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,697 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931642830 | Status: 500 | Duration: 54.64ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,697 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931642830 | Status: 500 | Duration: 54.64ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,697 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931642830 | Status: 500 | Duration: 54.64ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,697 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931642830 | Status: 500 | Duration: 54.64ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,697 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931642830 | Status: 500 | Duration: 54.64ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,697 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931642830 | Status: 500 | Duration: 54.64ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:11,697 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631931642830 | Status: 500 | Duration: 54.64ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:12,136 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:12,136 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:12,136 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:12,136 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:12,136 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:12,136 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:12,136 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:12,136 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:12,136 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:12,136 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:12:12,145 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:12,145 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:12,145 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:12,145 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:12,145 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:12,145 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:12,145 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:12,145 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:12,145 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:12,145 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:12,145 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:12:12,148 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:12,148 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:12,148 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:12,148 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:12,148 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:12,148 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:12,148 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:12,148 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:12,148 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:12,148 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:12,148 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:12:12,152 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631932151193 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:12,152 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631932151193 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:12,152 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631932151193 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:12,152 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631932151193 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:12,152 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631932151193 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:12,152 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631932151193 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:12,152 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631932151193 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:12,152 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631932151193 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:12,152 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631932151193 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:12,152 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631932151193 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:12,152 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752631932151193 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:12:12,154 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:12,154 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:12,154 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:12,154 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:12,154 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:12,154 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:12,154 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:12,154 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:12,154 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:12,154 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:12,154 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:12,159 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631932151193 | Status: 500 | Duration: 7.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:12,159 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631932151193 | Status: 500 | Duration: 7.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:12,159 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631932151193 | Status: 500 | Duration: 7.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:12,159 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631932151193 | Status: 500 | Duration: 7.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:12,159 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631932151193 | Status: 500 | Duration: 7.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:12,159 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631932151193 | Status: 500 | Duration: 7.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:12,159 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631932151193 | Status: 500 | Duration: 7.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:12,159 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631932151193 | Status: 500 | Duration: 7.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:12,159 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631932151193 | Status: 500 | Duration: 7.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:12,159 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631932151193 | Status: 500 | Duration: 7.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:12:12,159 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752631932151193 | Status: 500 | Duration: 7.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:29,162 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:29,177 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:29,183 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632009180057 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:29,184 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632009180057 | Status: 400 | Duration: 4.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:40,106 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:40,120 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:40,128 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632020125818 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:40,128 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632020125818 | Status: 400 | Duration: 3.09ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:50,854 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:50,858 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:50,866 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:50,872 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:50,872 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:50,874 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:50,874 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:50,876 - vibmon_app - INFO - [API_REQUEST] GET api.get_status | RequestID: 1752632030875453 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:50,876 - vibmon_app - INFO - [API_REQUEST] GET api.get_status | RequestID: 1752632030875453 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:50,877 - vibmon_app - INFO - [API_CALL] status | Method: GET | IP: 127.0.0.1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:37 in get_status()]
2025-07-16 10:13:50,877 - vibmon_app - INFO - [API_CALL] status | Method: GET | IP: 127.0.0.1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:37 in get_status()]
2025-07-16 10:13:50,877 - vibmon_app - INFO - [API_RESPONSE] GET api.get_status | RequestID: 1752632030875453 | Status: 200 | Duration: 2.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:50,877 - vibmon_app - INFO - [API_RESPONSE] GET api.get_status | RequestID: 1752632030875453 | Status: 200 | Duration: 2.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:50,880 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:50,880 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:50,887 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:50,887 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:50,887 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:50,890 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:50,890 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:50,890 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:50,936 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632030936452 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpxjbfz5iv.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:50,936 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632030936452 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpxjbfz5iv.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:50,936 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632030936452 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpxjbfz5iv.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:50,937 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpxjbfz5iv.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:82 in get_freq_am()]
2025-07-16 10:13:50,937 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpxjbfz5iv.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:82 in get_freq_am()]
2025-07-16 10:13:50,937 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpxjbfz5iv.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:82 in get_freq_am()]
2025-07-16 10:13:50,938 - vibmon_app - INFO - [PARAMS] 频率幅值计算 | RequestID: 1752632030936452 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpxjbfz5iv.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:13:50,938 - vibmon_app - INFO - [PARAMS] 频率幅值计算 | RequestID: 1752632030936452 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpxjbfz5iv.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:13:50,938 - vibmon_app - INFO - [PARAMS] 频率幅值计算 | RequestID: 1752632030936452 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpxjbfz5iv.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:13:50,939 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752632030936452 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:50,939 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752632030936452 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:50,939 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752632030936452 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:50,956 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752632030936452 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:50,956 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752632030936452 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:50,956 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752632030936452 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:50,957 - vibmon_app - INFO - [PROCESSING] 开始包络谱计算 | RequestID: 1752632030936452 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:50,957 - vibmon_app - INFO - [PROCESSING] 开始包络谱计算 | RequestID: 1752632030936452 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:50,957 - vibmon_app - INFO - [PROCESSING] 开始包络谱计算 | RequestID: 1752632030936452 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:50,960 - vibmon_app - INFO - [PROCESSING] 包络谱计算完成 | RequestID: 1752632030936452 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:50,960 - vibmon_app - INFO - [PROCESSING] 包络谱计算完成 | RequestID: 1752632030936452 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:50,960 - vibmon_app - INFO - [PROCESSING] 包络谱计算完成 | RequestID: 1752632030936452 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:50,960 - vibmon_app - INFO - [RESULT] 频率幅值计算完成 | RequestID: 1752632030936452 | 结果摘要: {"status": "success", "key_metrics": {}} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:258 in log_result_summary()]
2025-07-16 10:13:50,960 - vibmon_app - INFO - [RESULT] 频率幅值计算完成 | RequestID: 1752632030936452 | 结果摘要: {"status": "success", "key_metrics": {}} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:258 in log_result_summary()]
2025-07-16 10:13:50,960 - vibmon_app - INFO - [RESULT] 频率幅值计算完成 | RequestID: 1752632030936452 | 结果摘要: {"status": "success", "key_metrics": {}} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:258 in log_result_summary()]
2025-07-16 10:13:50,994 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632030936452 | Status: 200 | Duration: 58.36ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:50,994 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632030936452 | Status: 200 | Duration: 58.36ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:50,994 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632030936452 | Status: 200 | Duration: 58.36ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,008 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,008 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,008 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,016 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,016 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,016 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,016 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,019 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,019 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,019 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,019 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,021 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031021762 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,021 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031021762 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,021 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031021762 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,021 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031021762 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,022 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:82 in get_freq_am()]
2025-07-16 10:13:51,022 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:82 in get_freq_am()]
2025-07-16 10:13:51,022 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:82 in get_freq_am()]
2025-07-16 10:13:51,022 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:82 in get_freq_am()]
2025-07-16 10:13:51,023 - vibmon_app - WARNING - 频率幅值数据验证错误: 缺少必要参数: data_path [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:172 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 90, in get_freq_am
    validated_data = RequestValidator.validate_freq_am_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 40, in validate_freq_am_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: data_path
2025-07-16 10:13:51,023 - vibmon_app - WARNING - 频率幅值数据验证错误: 缺少必要参数: data_path [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:172 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 90, in get_freq_am
    validated_data = RequestValidator.validate_freq_am_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 40, in validate_freq_am_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: data_path
2025-07-16 10:13:51,023 - vibmon_app - WARNING - 频率幅值数据验证错误: 缺少必要参数: data_path [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:172 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 90, in get_freq_am
    validated_data = RequestValidator.validate_freq_am_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 40, in validate_freq_am_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: data_path
2025-07-16 10:13:51,023 - vibmon_app - WARNING - 频率幅值数据验证错误: 缺少必要参数: data_path [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:172 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 90, in get_freq_am
    validated_data = RequestValidator.validate_freq_am_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 40, in validate_freq_am_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: data_path
2025-07-16 10:13:51,024 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031021762 | Status: 400 | Duration: 3.03ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,024 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031021762 | Status: 400 | Duration: 3.03ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,024 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031021762 | Status: 400 | Duration: 3.03ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,024 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031021762 | Status: 400 | Duration: 3.03ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,028 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,028 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,028 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,028 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,035 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,035 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,035 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,035 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,035 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,037 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,037 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,037 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,037 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,037 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,039 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031039734 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "nonexistent_file.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,039 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031039734 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "nonexistent_file.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,039 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031039734 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "nonexistent_file.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,039 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031039734 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "nonexistent_file.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,039 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031039734 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "nonexistent_file.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,040 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'nonexistent_file.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:82 in get_freq_am()]
2025-07-16 10:13:51,040 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'nonexistent_file.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:82 in get_freq_am()]
2025-07-16 10:13:51,040 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'nonexistent_file.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:82 in get_freq_am()]
2025-07-16 10:13:51,040 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'nonexistent_file.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:82 in get_freq_am()]
2025-07-16 10:13:51,040 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'nonexistent_file.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:82 in get_freq_am()]
2025-07-16 10:13:51,041 - vibmon_app - WARNING - 频率幅值请求参数错误: 文件不存在: nonexistent_file.csv [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:164 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 90, in get_freq_am
    validated_data = RequestValidator.validate_freq_am_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 54, in validate_freq_am_request
    check_file_exists(data_path)
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\helpers.py", line 82, in check_file_exists
    raise FileNotFoundError(f"文件不存在: {file_path}")
FileNotFoundError: 文件不存在: nonexistent_file.csv
2025-07-16 10:13:51,041 - vibmon_app - WARNING - 频率幅值请求参数错误: 文件不存在: nonexistent_file.csv [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:164 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 90, in get_freq_am
    validated_data = RequestValidator.validate_freq_am_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 54, in validate_freq_am_request
    check_file_exists(data_path)
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\helpers.py", line 82, in check_file_exists
    raise FileNotFoundError(f"文件不存在: {file_path}")
FileNotFoundError: 文件不存在: nonexistent_file.csv
2025-07-16 10:13:51,041 - vibmon_app - WARNING - 频率幅值请求参数错误: 文件不存在: nonexistent_file.csv [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:164 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 90, in get_freq_am
    validated_data = RequestValidator.validate_freq_am_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 54, in validate_freq_am_request
    check_file_exists(data_path)
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\helpers.py", line 82, in check_file_exists
    raise FileNotFoundError(f"文件不存在: {file_path}")
FileNotFoundError: 文件不存在: nonexistent_file.csv
2025-07-16 10:13:51,041 - vibmon_app - WARNING - 频率幅值请求参数错误: 文件不存在: nonexistent_file.csv [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:164 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 90, in get_freq_am
    validated_data = RequestValidator.validate_freq_am_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 54, in validate_freq_am_request
    check_file_exists(data_path)
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\helpers.py", line 82, in check_file_exists
    raise FileNotFoundError(f"文件不存在: {file_path}")
FileNotFoundError: 文件不存在: nonexistent_file.csv
2025-07-16 10:13:51,041 - vibmon_app - WARNING - 频率幅值请求参数错误: 文件不存在: nonexistent_file.csv [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:164 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 90, in get_freq_am
    validated_data = RequestValidator.validate_freq_am_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 54, in validate_freq_am_request
    check_file_exists(data_path)
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\helpers.py", line 82, in check_file_exists
    raise FileNotFoundError(f"文件不存在: {file_path}")
FileNotFoundError: 文件不存在: nonexistent_file.csv
2025-07-16 10:13:51,043 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031039734 | Status: 400 | Duration: 3.99ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,043 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031039734 | Status: 400 | Duration: 3.99ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,043 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031039734 | Status: 400 | Duration: 3.99ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,043 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031039734 | Status: 400 | Duration: 3.99ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,043 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031039734 | Status: 400 | Duration: 3.99ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,047 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,047 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,047 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,047 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,047 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,055 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,055 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,055 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,055 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,055 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,055 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,057 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,057 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,057 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,057 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,057 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,057 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,061 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031061247 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpdqhtjgnm.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,061 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031061247 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpdqhtjgnm.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,061 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031061247 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpdqhtjgnm.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,061 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031061247 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpdqhtjgnm.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,061 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031061247 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpdqhtjgnm.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,061 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031061247 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpdqhtjgnm.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,062 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpdqhtjgnm.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:82 in get_freq_am()]
2025-07-16 10:13:51,062 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpdqhtjgnm.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:82 in get_freq_am()]
2025-07-16 10:13:51,062 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpdqhtjgnm.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:82 in get_freq_am()]
2025-07-16 10:13:51,062 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpdqhtjgnm.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:82 in get_freq_am()]
2025-07-16 10:13:51,062 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpdqhtjgnm.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:82 in get_freq_am()]
2025-07-16 10:13:51,062 - vibmon_app - INFO - [API_CALL] freq_am | Method: POST | IP: 127.0.0.1 | Params: {'equipment_code': 'TEST001', 'data_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpdqhtjgnm.csv'} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:82 in get_freq_am()]
2025-07-16 10:13:51,063 - vibmon_app - INFO - [PARAMS] 频率幅值计算 | RequestID: 1752632031061247 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpdqhtjgnm.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:13:51,063 - vibmon_app - INFO - [PARAMS] 频率幅值计算 | RequestID: 1752632031061247 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpdqhtjgnm.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:13:51,063 - vibmon_app - INFO - [PARAMS] 频率幅值计算 | RequestID: 1752632031061247 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpdqhtjgnm.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:13:51,063 - vibmon_app - INFO - [PARAMS] 频率幅值计算 | RequestID: 1752632031061247 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpdqhtjgnm.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:13:51,063 - vibmon_app - INFO - [PARAMS] 频率幅值计算 | RequestID: 1752632031061247 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpdqhtjgnm.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:13:51,063 - vibmon_app - INFO - [PARAMS] 频率幅值计算 | RequestID: 1752632031061247 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpdqhtjgnm.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:13:51,064 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752632031061247 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,064 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752632031061247 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,064 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752632031061247 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,064 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752632031061247 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,064 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752632031061247 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,064 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752632031061247 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,067 - vibmon_app - WARNING - 频率幅值请求参数错误: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2'] [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:164 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 97, in get_freq_am
    data_h, data_a, data_v, sampling_rate = RequestValidator.load_and_validate_csv(
                                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 157, in load_and_validate_csv
    validate_csv_columns(csv_data, required_columns)
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\helpers.py", line 103, in validate_csv_columns
    raise ValueError(
ValueError: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2']
2025-07-16 10:13:51,067 - vibmon_app - WARNING - 频率幅值请求参数错误: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2'] [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:164 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 97, in get_freq_am
    data_h, data_a, data_v, sampling_rate = RequestValidator.load_and_validate_csv(
                                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 157, in load_and_validate_csv
    validate_csv_columns(csv_data, required_columns)
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\helpers.py", line 103, in validate_csv_columns
    raise ValueError(
ValueError: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2']
2025-07-16 10:13:51,067 - vibmon_app - WARNING - 频率幅值请求参数错误: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2'] [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:164 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 97, in get_freq_am
    data_h, data_a, data_v, sampling_rate = RequestValidator.load_and_validate_csv(
                                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 157, in load_and_validate_csv
    validate_csv_columns(csv_data, required_columns)
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\helpers.py", line 103, in validate_csv_columns
    raise ValueError(
ValueError: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2']
2025-07-16 10:13:51,067 - vibmon_app - WARNING - 频率幅值请求参数错误: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2'] [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:164 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 97, in get_freq_am
    data_h, data_a, data_v, sampling_rate = RequestValidator.load_and_validate_csv(
                                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 157, in load_and_validate_csv
    validate_csv_columns(csv_data, required_columns)
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\helpers.py", line 103, in validate_csv_columns
    raise ValueError(
ValueError: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2']
2025-07-16 10:13:51,067 - vibmon_app - WARNING - 频率幅值请求参数错误: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2'] [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:164 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 97, in get_freq_am
    data_h, data_a, data_v, sampling_rate = RequestValidator.load_and_validate_csv(
                                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 157, in load_and_validate_csv
    validate_csv_columns(csv_data, required_columns)
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\helpers.py", line 103, in validate_csv_columns
    raise ValueError(
ValueError: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2']
2025-07-16 10:13:51,067 - vibmon_app - WARNING - 频率幅值请求参数错误: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2'] [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:164 in get_freq_am()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 97, in get_freq_am
    data_h, data_a, data_v, sampling_rate = RequestValidator.load_and_validate_csv(
                                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 157, in load_and_validate_csv
    validate_csv_columns(csv_data, required_columns)
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\helpers.py", line 103, in validate_csv_columns
    raise ValueError(
ValueError: CSV文件缺少必要的列: ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']. 可用列: ['wrong_column1', 'wrong_column2']
2025-07-16 10:13:51,068 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031061247 | Status: 400 | Duration: 7.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,068 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031061247 | Status: 400 | Duration: 7.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,068 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031061247 | Status: 400 | Duration: 7.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,068 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031061247 | Status: 400 | Duration: 7.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,068 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031061247 | Status: 400 | Duration: 7.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,068 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031061247 | Status: 400 | Duration: 7.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,072 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,072 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,072 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,072 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,072 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,072 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,081 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,081 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,081 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,081 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,081 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,081 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,081 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,084 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,084 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,084 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,084 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,084 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,084 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,084 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,130 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031130339 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzuvko3jo.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,130 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031130339 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzuvko3jo.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,130 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031130339 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzuvko3jo.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,130 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031130339 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzuvko3jo.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,130 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031130339 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzuvko3jo.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,130 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031130339 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzuvko3jo.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,130 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031130339 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzuvko3jo.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,133 - vibmon_app - INFO - [PARAMS] 多特征计算 | RequestID: 1752632031130339 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzuvko3jo.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:13:51,133 - vibmon_app - INFO - [PARAMS] 多特征计算 | RequestID: 1752632031130339 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzuvko3jo.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:13:51,133 - vibmon_app - INFO - [PARAMS] 多特征计算 | RequestID: 1752632031130339 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzuvko3jo.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:13:51,133 - vibmon_app - INFO - [PARAMS] 多特征计算 | RequestID: 1752632031130339 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzuvko3jo.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:13:51,133 - vibmon_app - INFO - [PARAMS] 多特征计算 | RequestID: 1752632031130339 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzuvko3jo.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:13:51,133 - vibmon_app - INFO - [PARAMS] 多特征计算 | RequestID: 1752632031130339 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzuvko3jo.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:13:51,133 - vibmon_app - INFO - [PARAMS] 多特征计算 | RequestID: 1752632031130339 | 关键参数: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpzuvko3jo.csv", "predict_flag": 0, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-16 10:13:51,134 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,134 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,134 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,134 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,134 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,134 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,134 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,150 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,150 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,150 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,150 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,150 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,150 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,150 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,152 - vibmon_app - INFO - [PROCESSING] 开始特征提取 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,152 - vibmon_app - INFO - [PROCESSING] 开始特征提取 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,152 - vibmon_app - INFO - [PROCESSING] 开始特征提取 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,152 - vibmon_app - INFO - [PROCESSING] 开始特征提取 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,152 - vibmon_app - INFO - [PROCESSING] 开始特征提取 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,152 - vibmon_app - INFO - [PROCESSING] 开始特征提取 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,152 - vibmon_app - INFO - [PROCESSING] 开始特征提取 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,155 - vibmon_app - INFO - [PROCESSING] 特征提取完成 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,155 - vibmon_app - INFO - [PROCESSING] 特征提取完成 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,155 - vibmon_app - INFO - [PROCESSING] 特征提取完成 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,155 - vibmon_app - INFO - [PROCESSING] 特征提取完成 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,155 - vibmon_app - INFO - [PROCESSING] 特征提取完成 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,155 - vibmon_app - INFO - [PROCESSING] 特征提取完成 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,155 - vibmon_app - INFO - [PROCESSING] 特征提取完成 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,156 - vibmon_app - INFO - [PROCESSING] 开始健康度评估 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,156 - vibmon_app - INFO - [PROCESSING] 开始健康度评估 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,156 - vibmon_app - INFO - [PROCESSING] 开始健康度评估 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,156 - vibmon_app - INFO - [PROCESSING] 开始健康度评估 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,156 - vibmon_app - INFO - [PROCESSING] 开始健康度评估 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,156 - vibmon_app - INFO - [PROCESSING] 开始健康度评估 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,156 - vibmon_app - INFO - [PROCESSING] 开始健康度评估 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,158 - vibmon_app - INFO - [PROCESSING] 健康度评估完成 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,158 - vibmon_app - INFO - [PROCESSING] 健康度评估完成 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,158 - vibmon_app - INFO - [PROCESSING] 健康度评估完成 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,158 - vibmon_app - INFO - [PROCESSING] 健康度评估完成 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,158 - vibmon_app - INFO - [PROCESSING] 健康度评估完成 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,158 - vibmon_app - INFO - [PROCESSING] 健康度评估完成 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,158 - vibmon_app - INFO - [PROCESSING] 健康度评估完成 | RequestID: 1752632031130339 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-16 10:13:51,160 - vibmon_app - INFO - [RESULT] 多特征计算完成 | RequestID: 1752632031130339 | 结果摘要: {"status": "success", "key_metrics": {"health_degree": 100.0, "rms_values_total": 1.0748797336275226, "total_amplitude": 0.6210548299930087}} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:258 in log_result_summary()]
2025-07-16 10:13:51,160 - vibmon_app - INFO - [RESULT] 多特征计算完成 | RequestID: 1752632031130339 | 结果摘要: {"status": "success", "key_metrics": {"health_degree": 100.0, "rms_values_total": 1.0748797336275226, "total_amplitude": 0.6210548299930087}} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:258 in log_result_summary()]
2025-07-16 10:13:51,160 - vibmon_app - INFO - [RESULT] 多特征计算完成 | RequestID: 1752632031130339 | 结果摘要: {"status": "success", "key_metrics": {"health_degree": 100.0, "rms_values_total": 1.0748797336275226, "total_amplitude": 0.6210548299930087}} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:258 in log_result_summary()]
2025-07-16 10:13:51,160 - vibmon_app - INFO - [RESULT] 多特征计算完成 | RequestID: 1752632031130339 | 结果摘要: {"status": "success", "key_metrics": {"health_degree": 100.0, "rms_values_total": 1.0748797336275226, "total_amplitude": 0.6210548299930087}} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:258 in log_result_summary()]
2025-07-16 10:13:51,160 - vibmon_app - INFO - [RESULT] 多特征计算完成 | RequestID: 1752632031130339 | 结果摘要: {"status": "success", "key_metrics": {"health_degree": 100.0, "rms_values_total": 1.0748797336275226, "total_amplitude": 0.6210548299930087}} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:258 in log_result_summary()]
2025-07-16 10:13:51,160 - vibmon_app - INFO - [RESULT] 多特征计算完成 | RequestID: 1752632031130339 | 结果摘要: {"status": "success", "key_metrics": {"health_degree": 100.0, "rms_values_total": 1.0748797336275226, "total_amplitude": 0.6210548299930087}} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:258 in log_result_summary()]
2025-07-16 10:13:51,160 - vibmon_app - INFO - [RESULT] 多特征计算完成 | RequestID: 1752632031130339 | 结果摘要: {"status": "success", "key_metrics": {"health_degree": 100.0, "rms_values_total": 1.0748797336275226, "total_amplitude": 0.6210548299930087}} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:258 in log_result_summary()]
2025-07-16 10:13:51,162 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031130339 | Status: 200 | Duration: 31.74ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,162 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031130339 | Status: 200 | Duration: 31.74ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,162 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031130339 | Status: 200 | Duration: 31.74ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,162 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031130339 | Status: 200 | Duration: 31.74ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,162 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031130339 | Status: 200 | Duration: 31.74ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,162 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031130339 | Status: 200 | Duration: 31.74ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,162 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031130339 | Status: 200 | Duration: 31.74ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,167 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,167 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,167 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,167 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,167 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,167 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,167 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,175 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,175 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,175 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,175 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,175 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,175 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,175 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,175 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,178 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,178 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,178 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,178 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,178 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,178 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,178 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,178 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,181 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031180076 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "test.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,181 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031180076 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "test.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,181 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031180076 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "test.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,181 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031180076 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "test.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,181 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031180076 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "test.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,181 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031180076 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "test.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,181 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031180076 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "test.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,181 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031180076 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "test.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,182 - vibmon_app - WARNING - 多特征数据验证错误: 缺少必要参数: health_degree_flag [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:363 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 223, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 83, in validate_multi_features_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: health_degree_flag
2025-07-16 10:13:51,182 - vibmon_app - WARNING - 多特征数据验证错误: 缺少必要参数: health_degree_flag [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:363 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 223, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 83, in validate_multi_features_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: health_degree_flag
2025-07-16 10:13:51,182 - vibmon_app - WARNING - 多特征数据验证错误: 缺少必要参数: health_degree_flag [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:363 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 223, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 83, in validate_multi_features_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: health_degree_flag
2025-07-16 10:13:51,182 - vibmon_app - WARNING - 多特征数据验证错误: 缺少必要参数: health_degree_flag [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:363 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 223, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 83, in validate_multi_features_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: health_degree_flag
2025-07-16 10:13:51,182 - vibmon_app - WARNING - 多特征数据验证错误: 缺少必要参数: health_degree_flag [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:363 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 223, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 83, in validate_multi_features_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: health_degree_flag
2025-07-16 10:13:51,182 - vibmon_app - WARNING - 多特征数据验证错误: 缺少必要参数: health_degree_flag [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:363 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 223, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 83, in validate_multi_features_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: health_degree_flag
2025-07-16 10:13:51,182 - vibmon_app - WARNING - 多特征数据验证错误: 缺少必要参数: health_degree_flag [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:363 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 223, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 83, in validate_multi_features_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: health_degree_flag
2025-07-16 10:13:51,182 - vibmon_app - WARNING - 多特征数据验证错误: 缺少必要参数: health_degree_flag [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:363 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 223, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 83, in validate_multi_features_request
    raise InvalidDataError(f"缺少必要参数: {param}")
vibmon_app.utils.exceptions.InvalidDataError: 缺少必要参数: health_degree_flag
2025-07-16 10:13:51,184 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031180076 | Status: 400 | Duration: 4.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,184 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031180076 | Status: 400 | Duration: 4.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,184 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031180076 | Status: 400 | Duration: 4.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,184 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031180076 | Status: 400 | Duration: 4.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,184 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031180076 | Status: 400 | Duration: 4.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,184 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031180076 | Status: 400 | Duration: 4.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,184 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031180076 | Status: 400 | Duration: 4.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,184 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031180076 | Status: 400 | Duration: 4.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,188 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,188 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,188 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,188 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,188 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,188 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,188 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,188 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,196 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,196 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,196 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,196 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,196 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,196 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,196 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,196 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,196 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,199 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,199 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,199 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,199 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,199 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,199 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,199 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,199 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,199 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,245 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031245883 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmppf_ax55y.csv", "predict_flag": 0, "health_degree_flag": 2} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,245 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031245883 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmppf_ax55y.csv", "predict_flag": 0, "health_degree_flag": 2} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,245 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031245883 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmppf_ax55y.csv", "predict_flag": 0, "health_degree_flag": 2} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,245 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031245883 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmppf_ax55y.csv", "predict_flag": 0, "health_degree_flag": 2} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,245 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031245883 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmppf_ax55y.csv", "predict_flag": 0, "health_degree_flag": 2} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,245 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031245883 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmppf_ax55y.csv", "predict_flag": 0, "health_degree_flag": 2} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,245 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031245883 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmppf_ax55y.csv", "predict_flag": 0, "health_degree_flag": 2} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,245 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031245883 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmppf_ax55y.csv", "predict_flag": 0, "health_degree_flag": 2} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,245 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752632031245883 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "TEST001", "data_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmppf_ax55y.csv", "predict_flag": 0, "health_degree_flag": 2} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,247 - vibmon_app - WARNING - 多特征数据验证错误: health_degree_flag必须是0或1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:363 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 223, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 110, in validate_multi_features_request
    raise InvalidDataError("health_degree_flag必须是0或1")
vibmon_app.utils.exceptions.InvalidDataError: health_degree_flag必须是0或1
2025-07-16 10:13:51,247 - vibmon_app - WARNING - 多特征数据验证错误: health_degree_flag必须是0或1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:363 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 223, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 110, in validate_multi_features_request
    raise InvalidDataError("health_degree_flag必须是0或1")
vibmon_app.utils.exceptions.InvalidDataError: health_degree_flag必须是0或1
2025-07-16 10:13:51,247 - vibmon_app - WARNING - 多特征数据验证错误: health_degree_flag必须是0或1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:363 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 223, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 110, in validate_multi_features_request
    raise InvalidDataError("health_degree_flag必须是0或1")
vibmon_app.utils.exceptions.InvalidDataError: health_degree_flag必须是0或1
2025-07-16 10:13:51,247 - vibmon_app - WARNING - 多特征数据验证错误: health_degree_flag必须是0或1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:363 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 223, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 110, in validate_multi_features_request
    raise InvalidDataError("health_degree_flag必须是0或1")
vibmon_app.utils.exceptions.InvalidDataError: health_degree_flag必须是0或1
2025-07-16 10:13:51,247 - vibmon_app - WARNING - 多特征数据验证错误: health_degree_flag必须是0或1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:363 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 223, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 110, in validate_multi_features_request
    raise InvalidDataError("health_degree_flag必须是0或1")
vibmon_app.utils.exceptions.InvalidDataError: health_degree_flag必须是0或1
2025-07-16 10:13:51,247 - vibmon_app - WARNING - 多特征数据验证错误: health_degree_flag必须是0或1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:363 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 223, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 110, in validate_multi_features_request
    raise InvalidDataError("health_degree_flag必须是0或1")
vibmon_app.utils.exceptions.InvalidDataError: health_degree_flag必须是0或1
2025-07-16 10:13:51,247 - vibmon_app - WARNING - 多特征数据验证错误: health_degree_flag必须是0或1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:363 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 223, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 110, in validate_multi_features_request
    raise InvalidDataError("health_degree_flag必须是0或1")
vibmon_app.utils.exceptions.InvalidDataError: health_degree_flag必须是0或1
2025-07-16 10:13:51,247 - vibmon_app - WARNING - 多特征数据验证错误: health_degree_flag必须是0或1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:363 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 223, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 110, in validate_multi_features_request
    raise InvalidDataError("health_degree_flag必须是0或1")
vibmon_app.utils.exceptions.InvalidDataError: health_degree_flag必须是0或1
2025-07-16 10:13:51,247 - vibmon_app - WARNING - 多特征数据验证错误: health_degree_flag必须是0或1 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:363 in get_multi_features()]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 223, in get_multi_features
    validated_data = RequestValidator.validate_multi_features_request(json_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\validators.py", line 110, in validate_multi_features_request
    raise InvalidDataError("health_degree_flag必须是0或1")
vibmon_app.utils.exceptions.InvalidDataError: health_degree_flag必须是0或1
2025-07-16 10:13:51,250 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031245883 | Status: 400 | Duration: 5.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,250 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031245883 | Status: 400 | Duration: 5.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,250 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031245883 | Status: 400 | Duration: 5.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,250 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031245883 | Status: 400 | Duration: 5.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,250 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031245883 | Status: 400 | Duration: 5.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,250 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031245883 | Status: 400 | Duration: 5.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,250 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031245883 | Status: 400 | Duration: 5.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,250 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031245883 | Status: 400 | Duration: 5.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,250 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752632031245883 | Status: 400 | Duration: 5.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,257 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,257 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,257 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,257 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,257 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,257 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,257 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,257 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,257 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,265 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,265 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,265 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,265 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,265 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,265 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,265 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,265 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,265 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,265 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,269 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,269 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,269 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,269 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,269 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,269 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,269 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,269 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,269 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,269 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,272 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031271408 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,272 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031271408 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,272 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031271408 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,272 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031271408 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,272 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031271408 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,272 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031271408 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,272 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031271408 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,272 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031271408 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,272 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031271408 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,272 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031271408 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,273 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031271408 | Status: 400 | Duration: 2.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,273 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031271408 | Status: 400 | Duration: 2.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,273 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031271408 | Status: 400 | Duration: 2.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,273 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031271408 | Status: 400 | Duration: 2.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,273 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031271408 | Status: 400 | Duration: 2.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,273 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031271408 | Status: 400 | Duration: 2.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,273 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031271408 | Status: 400 | Duration: 2.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,273 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031271408 | Status: 400 | Duration: 2.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,273 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031271408 | Status: 400 | Duration: 2.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,273 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031271408 | Status: 400 | Duration: 2.00ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,278 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,278 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,278 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,278 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,278 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,278 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,278 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,278 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,278 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,278 - vibmon_app - INFO - [PRODUCTION] 生产模式 - 启用文件日志记录 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:90 in configure_logging()]
2025-07-16 10:13:51,287 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,287 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,287 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,287 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,287 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,287 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,287 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,287 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,287 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,287 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,287 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-16 10:13:51,290 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,290 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,290 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,290 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,290 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,290 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,290 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,290 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,290 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,290 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,290 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-16 10:13:51,293 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031292628 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,293 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031292628 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,293 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031292628 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,293 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031292628 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,293 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031292628 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,293 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031292628 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,293 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031292628 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,293 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031292628 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,293 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031292628 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,293 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031292628 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,293 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752632031292628 | IP: 127.0.0.1 | KeyParams: {} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-16 10:13:51,295 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031292628 | Status: 400 | Duration: 2.99ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,295 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031292628 | Status: 400 | Duration: 2.99ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,295 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031292628 | Status: 400 | Duration: 2.99ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,295 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031292628 | Status: 400 | Duration: 2.99ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,295 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031292628 | Status: 400 | Duration: 2.99ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,295 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031292628 | Status: 400 | Duration: 2.99ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,295 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031292628 | Status: 400 | Duration: 2.99ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,295 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031292628 | Status: 400 | Duration: 2.99ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,295 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031292628 | Status: 400 | Duration: 2.99ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,295 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031292628 | Status: 400 | Duration: 2.99ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-16 10:13:51,295 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752632031292628 | Status: 400 | Duration: 2.99ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-17 13:41:40,797 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-17 13:41:40,841 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-17 13:41:40,843 - vibmon_app - INFO - [DEVELOPMENT] 开发模式 - 日志仅输出到控制台，不创建日志文件 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:86 in configure_logging()]
2025-07-17 13:41:40,851 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-17 13:41:40,852 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-17 13:41:40,853 - vibmon_app - INFO - [STARTUP] 振动监测系统启动 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:347 in main()]
2025-07-17 13:41:40,853 - vibmon_app - INFO - [CONFIG] 模式: development | 日志级别: STANDARD [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:348 in main()]
2025-07-17 13:41:40,853 - vibmon_app - INFO - [CONFIG] 监听地址: 0.0.0.0:5000 | 调试模式: True [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:349 in main()]
2025-07-17 13:41:53,847 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-17 13:41:53,857 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-17 13:41:53,859 - vibmon_app - INFO - [DEVELOPMENT] 开发模式 - 日志仅输出到控制台，不创建日志文件 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:86 in configure_logging()]
2025-07-17 13:41:53,866 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-17 13:41:53,868 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-17 13:41:53,868 - vibmon_app - INFO - [STARTUP] 振动监测系统启动 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:347 in main()]
2025-07-17 13:41:53,869 - vibmon_app - INFO - [CONFIG] 模式: development | 日志级别: STANDARD [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:348 in main()]
2025-07-17 13:41:53,869 - vibmon_app - INFO - [CONFIG] 监听地址: 0.0.0.0:5000 | 调试模式: True [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:349 in main()]
2025-07-17 14:10:31,942 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-17 14:10:31,957 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-17 14:10:31,959 - vibmon_app - INFO - [DEVELOPMENT] 开发模式 - 日志仅输出到控制台，不创建日志文件 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:86 in configure_logging()]
2025-07-17 14:10:31,966 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-17 14:10:31,967 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-17 14:10:31,968 - vibmon_app - INFO - [STARTUP] 振动监测系统启动 - development模式 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:325 in main()]
2025-07-17 14:10:31,968 - vibmon_app - INFO - [CONFIG] 监听: 0.0.0.0:5000 | 日志: standard [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:326 in main()]
2025-07-17 14:13:00,532 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-17 14:13:00,545 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-17 14:13:00,546 - vibmon_app - INFO - [DEVELOPMENT] 开发模式 - 日志仅输出到控制台，不创建日志文件 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:86 in configure_logging()]
2025-07-17 14:13:00,553 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-17 14:13:00,554 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-17 14:13:00,555 - vibmon_app - INFO - [STARTUP] 振动监测系统启动 - development模式 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:325 in main()]
2025-07-17 14:13:00,555 - vibmon_app - INFO - [CONFIG] 监听: 0.0.0.0:5000 | 日志: standard [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\run.py:326 in main()]
2025-07-17 14:14:07,997 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-17 14:14:09,843 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-17 14:14:09,845 - vibmon_app - INFO - [DEVELOPMENT] 开发模式 - 日志仅输出到控制台，不创建日志文件 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:86 in configure_logging()]
2025-07-17 14:14:09,851 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\models\ml_models.py:45 in load_svm_model()]
2025-07-17 14:14:09,854 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\__init__.py:45 in create_app()]
2025-07-17 14:14:38,293 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752732878292546 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "EQ123456", "data_path": "csv_data/0000.csv", "predict_flag": 1, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-17 14:14:38,294 - vibmon_app - INFO - [PARAMS] 多特征计算 | RequestID: 1752732878292546 | 关键参数: {"equipment_code": "EQ123456", "data_path": "csv_data/0000.csv", "predict_flag": 1, "health_degree_flag": 1} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:186 in log_request_params()]
2025-07-17 14:14:38,295 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752732878292546 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-17 14:14:38,328 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752732878292546 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-17 14:14:38,328 - vibmon_app - INFO - [PROCESSING] 开始特征提取 | RequestID: 1752732878292546 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-17 14:14:38,333 - vibmon_app - INFO - [PROCESSING] 特征提取完成 | RequestID: 1752732878292546 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-17 14:14:38,333 - vibmon_app - INFO - [PROCESSING] 开始SVM预测 | RequestID: 1752732878292546 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-17 14:14:38,348 - vibmon_app - INFO - [PROCESSING] SVM预测完成 | RequestID: 1752732878292546 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-17 14:14:38,348 - vibmon_app - INFO - [PROCESSING] 开始健康度评估 | RequestID: 1752732878292546 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-17 14:14:38,349 - vibmon_app - INFO - [PROCESSING] 健康度评估完成 | RequestID: 1752732878292546 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:226 in log_processing_step()]
2025-07-17 14:14:38,349 - vibmon_app - INFO - [RESULT] 多特征计算完成 | RequestID: 1752732878292546 | 结果摘要: {"status": "success", "key_metrics": {"predict_result": 1.0, "health_degree": 30, "rms_values_total": 0.37402753646389114, "total_amplitude": 0.19082382926516767}} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:258 in log_result_summary()]
2025-07-17 14:14:38,350 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752732878292546 | Status: 200 | Duration: 57.46ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-17 14:14:43,290 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752732883290597 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "EQ123456", "data_path": "csv_data/0002.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-17 14:14:43,291 - vibmon_app - ERROR - 频率幅值计算失败: 文件不存在: csv_data/0002.csv [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:230 in get_freq_am()]
2025-07-17 14:14:43,293 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752732883290597 | Status: 200 | Duration: 3.01ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-17 14:14:58,429 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752732898428026 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "EQ123456", "data_path": "csv_data/0001.csv"} [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:86 in wrapper()]
2025-07-17 14:14:58,429 - vibmon_app - INFO - [API_CALL] freq_am | Equipment: EQ123456 | Compress: False [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:124 in get_freq_am()]
2025-07-17 14:14:58,458 - vibmon_app - INFO - [FAST_CSV] 文件: 0001.csv | 样本数: 12000 | 采样率: 12000.0Hz [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\fast_csv_reader.py:91 in load_csv_fast()]
2025-07-17 14:14:58,468 - vibmon_app - INFO - [PERFORMANCE] freq_am | Total: 39.1ms | Load: 28.1ms | Compute: 8.0ms | Points: 6001 [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:213 in get_freq_am()]
2025-07-17 14:14:58,503 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752732898428026 | Status: 200 | Duration: 75.87ms [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\utils\logging_utils.py:122 in wrapper()]
2025-07-17 14:19:18,868 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 14:19:19,492 [INFO] [SUCCESS] Flask应用初始化完成
[SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
[SUCCESS] Flask应用初始化完成
2025-07-17 14:28:03,239 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 14:28:03,571 [INFO] [SUCCESS] Flask应用初始化完成
2025-07-17 14:31:32,538 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 14:31:32,552 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成
2025-07-17 14:31:32,553 - vibmon_app - INFO - [STARTUP] 振动监测系统启动 - production模式
2025-07-17 14:31:32,554 - vibmon_app - INFO - [CONFIG] 监听: 0.0.0.0:5000 | 日志: standard
2025-07-17 14:37:13,422 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 14:37:13,766 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成
2025-07-17 14:37:13,768 - vibmon_app - INFO - [STARTUP] 振动监测系统启动 - production模式
2025-07-17 14:37:13,769 - vibmon_app - INFO - [CONFIG] 监听: 0.0.0.0:5000 | 日志: standard
2025-07-17 14:38:09,239 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 14:38:09,563 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成
2025-07-17 14:38:09,564 - vibmon_app - INFO - [STARTUP] 振动监测系统启动 - production模式
2025-07-17 14:38:09,564 - vibmon_app - INFO - [CONFIG] 监听: 0.0.0.0:5000 | 日志: standard
2025-07-17 14:38:09,815 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 14:38:09,818 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成
2025-07-17 14:38:39,345 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 14:38:39,663 [INFO] [SUCCESS] Flask应用初始化完成
2025-07-17 14:38:39,896 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 14:38:39,898 [INFO] [SUCCESS] Flask应用初始化完成
2025-07-17 14:41:08,898 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 14:41:08,909 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成
2025-07-17 14:41:08,910 - vibmon_app - INFO - [STARTUP] 振动监测系统启动 - production模式
2025-07-17 14:41:08,911 - vibmon_app - INFO - [CONFIG] 监听: 0.0.0.0:5000 | 日志: standard
2025-07-17 14:42:52,315 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 14:42:52,326 [INFO] [SUCCESS] Flask应用初始化完成
2025-07-17 14:43:48,161 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 14:43:48,172 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成
2025-07-17 14:43:48,173 - vibmon_app - INFO - [STARTUP] 振动监测系统启动 - production模式
2025-07-17 14:43:48,173 - vibmon_app - INFO - [CONFIG] 监听: 0.0.0.0:5000 | 日志: standard
2025-07-17 14:43:48,278 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 14:43:48,280 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成
2025-07-17 14:44:02,549 - vibmon_app - INFO - [API_REQUEST] POST api.get_freq_am | RequestID: 1752734642549143 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "EQ123456", "data_path": "csv_data/0001.csv"}
2025-07-17 14:44:02,550 - vibmon_app - INFO - [API_CALL] freq_am | Equipment: EQ123456 | Compress: False
2025-07-17 14:44:02,580 - vibmon_app - INFO - [FAST_CSV] 文件: 0001.csv | 样本数: 12000 | 采样率: 12000.0Hz
2025-07-17 14:44:02,588 - vibmon_app - INFO - [PERFORMANCE] freq_am | Total: 38.1ms | Load: 30.1ms | Compute: 7.0ms | Points: 6001
2025-07-17 14:44:02,625 - vibmon_app - INFO - [API_RESPONSE] POST api.get_freq_am | RequestID: 1752734642549143 | Status: 200 | Duration: 76.76ms
2025-07-17 14:44:08,107 - vibmon_app - INFO - [API_REQUEST] POST api.get_multi_features | RequestID: 1752734648107232 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "EQ123456", "data_path": "csv_data/0000.csv", "predict_flag": 1, "health_degree_flag": 1}
2025-07-17 14:44:08,108 - vibmon_app - INFO - [PARAMS] 多特征计算 | RequestID: 1752734648107232 | 关键参数: {"equipment_code": "EQ123456", "data_path": "csv_data/0000.csv", "predict_flag": 1, "health_degree_flag": 1}
2025-07-17 14:44:08,109 - vibmon_app - INFO - [PROCESSING] 加载CSV数据 | RequestID: 1752734648107232
2025-07-17 14:44:08,123 - vibmon_app - INFO - [PROCESSING] 数据加载完成 | RequestID: 1752734648107232
2025-07-17 14:44:08,123 - vibmon_app - INFO - [PROCESSING] 开始特征提取 | RequestID: 1752734648107232
2025-07-17 14:44:08,128 - vibmon_app - INFO - [PROCESSING] 特征提取完成 | RequestID: 1752734648107232
2025-07-17 14:44:08,129 - vibmon_app - INFO - [PROCESSING] 开始SVM预测 | RequestID: 1752734648107232
2025-07-17 14:44:08,139 - vibmon_app - INFO - [PROCESSING] SVM预测完成 | RequestID: 1752734648107232
2025-07-17 14:44:08,140 - vibmon_app - INFO - [PROCESSING] 开始健康度评估 | RequestID: 1752734648107232
2025-07-17 14:44:08,142 - vibmon_app - INFO - [PROCESSING] 健康度评估完成 | RequestID: 1752734648107232
2025-07-17 14:44:08,142 - vibmon_app - INFO - [RESULT] 多特征计算完成 | RequestID: 1752734648107232 | 结果摘要: {"status": "success", "key_metrics": {"predict_result": 1.0, "health_degree": 30, "rms_values_total": 0.37402753646389114, "total_amplitude": 0.19082382926516767}}
2025-07-17 14:44:08,143 - vibmon_app - INFO - [API_RESPONSE] POST api.get_multi_features | RequestID: 1752734648107232 | Status: 200 | Duration: 36.14ms
2025-07-17 14:54:06,353 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 14:54:07,117 [INFO] [SUCCESS] Flask应用初始化完成
[SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
[SUCCESS] Flask应用初始化完成
2025-07-17 14:54:56,589 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 14:54:56,927 [INFO] [SUCCESS] Flask应用初始化完成
2025-07-17 15:10:05,959 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 15:10:06,343 [INFO] [SUCCESS] Flask应用初始化完成
[SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
[SUCCESS] Flask应用初始化完成
2025-07-17 15:11:34,487 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 15:11:34,832 [INFO] [SUCCESS] Flask应用初始化完成
2025-07-17 15:22:37,627 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 15:22:37,640 [INFO] [SUCCESS] Flask应用初始化完成
2025-07-17 15:22:55,846 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 15:22:55,858 [INFO] [SUCCESS] Flask应用初始化完成
2025-07-17 15:34:37,083 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 15:34:37,840 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成
2025-07-17 15:34:37,848 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 15:35:49,949 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 15:35:50,309 [INFO] [SUCCESS] Flask应用初始化完成
2025-07-17 15:38:40,871 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 15:38:40,888 [INFO] [SUCCESS] Flask应用初始化完成
2025-07-17 15:43:44,624 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 15:43:44,637 [INFO] [SUCCESS] Flask应用初始化完成
2025-07-17 15:44:13,570 [INFO] [API_REQUEST] POST api.get_freq_am | RequestID: 1752738253570970 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "EQ123456", "data_path": "csv_data/0001.csv"}
2025-07-17 15:44:13,572 [INFO] [API_CALL] freq_am | Equipment: EQ123456 | Compress: False
2025-07-17 15:44:13,602 [INFO] [FAST_CSV] 文件: 0001.csv | 样本数: 12000 | 采样率: 12000.0Hz
2025-07-17 15:44:13,615 [INFO] [PERFORMANCE] freq_am | Total: 43.5ms | Load: 28.6ms | Compute: 12.8ms | Points: 6001
2025-07-17 15:44:13,651 [INFO] [API_RESPONSE] POST api.get_freq_am | RequestID: 1752738253570970 | Status: 200 | Duration: 80.29ms
2025-07-17 15:44:18,411 [INFO] [API_REQUEST] POST api.get_freq_am | RequestID: 1752738258411527 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "EQ123456", "data_path": "csv_data/0001.csv"}
2025-07-17 15:44:18,412 [INFO] [API_CALL] freq_am | Equipment: EQ123456 | Compress: False
2025-07-17 15:44:18,419 [INFO] [FAST_CSV] 文件: 0001.csv | 样本数: 12000 | 采样率: 12000.0Hz
2025-07-17 15:44:18,424 [INFO] [PERFORMANCE] freq_am | Total: 12.1ms | Load: 8.1ms | Compute: 4.1ms | Points: 6001
2025-07-17 15:44:18,463 [INFO] [API_RESPONSE] POST api.get_freq_am | RequestID: 1752738258411527 | Status: 200 | Duration: 50.92ms
2025-07-17 16:00:08,999 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 16:00:09,674 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成
2025-07-17 16:17:39,034 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 16:17:39,627 [INFO] [SUCCESS] Flask应用初始化完成
2025-07-17 17:06:19,214 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 17:06:19,231 [INFO] [SUCCESS] Flask应用初始化完成
2025-07-17 17:06:22,269 [INFO] [API_REQUEST] POST api.get_freq_am | RequestID: 1752743182269467 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "EQ123456", "data_path": "csv_data/0001.csv"}
2025-07-17 17:06:22,270 [INFO] [API_CALL] freq_am | Equipment: EQ123456 | Compress: False
2025-07-17 17:06:22,299 [INFO] [FAST_CSV] 文件: 0001.csv | 样本数: 12000 | 采样率: 12000.0Hz
2025-07-17 17:06:22,311 [INFO] [PERFORMANCE] freq_am | Total: 42.2ms | Load: 29.0ms | Compute: 11.2ms | Points: 6001
2025-07-17 17:06:22,348 [INFO] [API_RESPONSE] POST api.get_freq_am | RequestID: 1752743182269467 | Status: 200 | Duration: 78.87ms
2025-07-17 18:55:33,299 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 18:55:33,319 [INFO] [SUCCESS] Flask应用初始化完成
2025-07-17 19:35:16,676 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-17 19:35:16,693 [INFO] [SUCCESS] Flask应用初始化完成
2025-07-17 19:39:12,900 [INFO] [API_REQUEST] GET api.get_status | RequestID: 1752752352900033 | IP: 127.0.0.1 | KeyParams: {}
2025-07-17 19:39:12,901 [INFO] [API_RESPONSE] GET api.get_status | RequestID: 1752752352900033 | Status: 200 | Duration: 1.16ms
2025-07-18 10:05:37,295 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-18 10:05:37,309 [INFO] [SUCCESS] Flask应用初始化完成
2025-07-18 10:05:43,506 [INFO] [API_REQUEST] POST api.get_freq_am | RequestID: 1752804343506881 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "EQ123456", "data_path": "csv_data/0000.csv"}
2025-07-18 10:05:43,507 [INFO] [API_CALL] freq_am | Equipment: EQ123456 | Compress: False
2025-07-18 10:05:43,518 [INFO] [FAST_CSV] 文件: 0000.csv | 样本数: 12000 | 采样率: 12000.0Hz
2025-07-18 10:05:43,524 [INFO] [PERFORMANCE] freq_am | Total: 16.8ms | Load: 9.5ms | Compute: 6.3ms | Points: 6001
2025-07-18 10:05:43,562 [INFO] [API_RESPONSE] POST api.get_freq_am | RequestID: 1752804343506881 | Status: 200 | Duration: 55.34ms
2025-07-18 10:05:45,690 [INFO] [API_REQUEST] POST api.get_freq_am | RequestID: 1752804345690604 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "EQ123456", "data_path": "csv_data/0000.csv"}
2025-07-18 10:05:45,690 [INFO] [API_CALL] freq_am | Equipment: EQ123456 | Compress: False
2025-07-18 10:05:45,699 [INFO] [FAST_CSV] 文件: 0000.csv | 样本数: 12000 | 采样率: 12000.0Hz
2025-07-18 10:05:45,703 [INFO] [PERFORMANCE] freq_am | Total: 12.6ms | Load: 8.5ms | Compute: 3.0ms | Points: 6001
2025-07-18 10:05:45,743 [INFO] [API_RESPONSE] POST api.get_freq_am | RequestID: 1752804345690604 | Status: 200 | Duration: 53.06ms
2025-07-18 10:05:47,531 [INFO] [API_REQUEST] POST api.get_freq_am | RequestID: 1752804347531026 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "EQ123456", "data_path": "csv_data/0000.csv"}
2025-07-18 10:05:47,532 [INFO] [API_CALL] freq_am | Equipment: EQ123456 | Compress: False
2025-07-18 10:05:47,540 [INFO] [FAST_CSV] 文件: 0000.csv | 样本数: 12000 | 采样率: 12000.0Hz
2025-07-18 10:05:47,544 [INFO] [PERFORMANCE] freq_am | Total: 12.6ms | Load: 7.2ms | Compute: 4.3ms | Points: 6001
2025-07-18 10:05:47,582 [INFO] [API_RESPONSE] POST api.get_freq_am | RequestID: 1752804347531026 | Status: 200 | Duration: 51.75ms
2025-07-18 10:05:48,670 [INFO] [API_REQUEST] POST api.get_freq_am | RequestID: 1752804348670197 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "EQ123456", "data_path": "csv_data/0000.csv"}
2025-07-18 10:05:48,671 [INFO] [API_CALL] freq_am | Equipment: EQ123456 | Compress: False
2025-07-18 10:05:48,681 [INFO] [FAST_CSV] 文件: 0000.csv | 样本数: 12000 | 采样率: 12000.0Hz
2025-07-18 10:05:48,685 [INFO] [PERFORMANCE] freq_am | Total: 14.1ms | Load: 10.0ms | Compute: 4.1ms | Points: 6001
2025-07-18 10:05:48,723 [INFO] [API_RESPONSE] POST api.get_freq_am | RequestID: 1752804348670197 | Status: 200 | Duration: 53.29ms
2025-07-18 10:06:04,794 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-18 10:06:04,803 [INFO] [SUCCESS] Flask应用初始化完成
2025-07-18 10:06:18,883 [INFO] [API_REQUEST] POST api.get_freq_am | RequestID: 1752804378883039 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "EQ123456", "data_path": "csv_data/0000.csv"}
2025-07-18 10:06:18,884 [INFO] [API_CALL] freq_am | Equipment: EQ123456 | Compress: False
2025-07-18 10:06:18,897 [INFO] [FAST_CSV] 文件: 0000.csv | 样本数: 12000 | 采样率: 12000.0Hz
2025-07-18 10:06:18,913 [INFO] [PERFORMANCE] freq_am | Total: 29.1ms | Load: 12.0ms | Compute: 15.8ms | Points: 6001
2025-07-18 10:06:18,949 [INFO] [API_RESPONSE] POST api.get_freq_am | RequestID: 1752804378883039 | Status: 200 | Duration: 66.88ms
2025-07-18 10:06:47,602 [INFO] [API_REQUEST] POST api.get_freq_am | RequestID: 1752804407602344 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "EQ123456", "data_path": "csv_data/0001.csv"}
2025-07-18 10:06:47,603 [INFO] [API_CALL] freq_am | Equipment: EQ123456 | Compress: False
2025-07-18 10:06:47,622 [INFO] [FAST_CSV] 文件: 0001.csv | 样本数: 12000 | 采样率: 12000.0Hz
2025-07-18 10:06:47,627 [INFO] [PERFORMANCE] freq_am | Total: 25.1ms | Load: 19.0ms | Compute: 5.0ms | Points: 6001
2025-07-18 10:06:47,664 [INFO] [API_RESPONSE] POST api.get_freq_am | RequestID: 1752804407602344 | Status: 200 | Duration: 61.84ms
2025-07-18 12:15:32,769 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-18 12:15:32,810 [INFO] [SUCCESS] Flask应用初始化完成
2025-07-18 12:16:10,959 [INFO] [API_REQUEST] POST api.get_freq_am | RequestID: 1752812170959909 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "EQ123456", "data_path": "csv_data/0001.csv"}
2025-07-18 12:16:10,962 [INFO] [API_CALL] freq_am | Equipment: EQ123456 | Compress: False
2025-07-18 12:16:11,020 [INFO] [FAST_CSV] 文件: 0001.csv | 样本数: 12000 | 采样率: 12000.0Hz
2025-07-18 12:16:11,038 [INFO] 信号处理器初始化: 4 线程
2025-07-18 12:16:11,046 [INFO] [PERFORMANCE] freq_am | Total: 84.8ms | Load: 51.8ms | Compute: 24.8ms | Points: 1500
2025-07-18 12:16:11,063 [INFO] [API_RESPONSE] POST api.get_freq_am | RequestID: 1752812170959909 | Status: 200 | Duration: 103.82ms
2025-07-18 12:16:35,190 [INFO] [API_REQUEST] POST api.get_freq_am | RequestID: 1752812195190966 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "EQ123456", "data_path": "csv_data/0001.csv"}
2025-07-18 12:16:35,190 [INFO] [API_CALL] freq_am | Equipment: EQ123456 | Compress: False
2025-07-18 12:16:35,197 [INFO] [FAST_CSV] 文件: 0001.csv | 样本数: 12000 | 采样率: 12000.0Hz
2025-07-18 12:16:35,198 [INFO] 信号处理器初始化: 4 线程
2025-07-18 12:16:35,202 [INFO] [PERFORMANCE] freq_am | Total: 12.0ms | Load: 7.0ms | Compute: 4.0ms | Points: 1500
2025-07-18 12:16:35,210 [INFO] [API_RESPONSE] POST api.get_freq_am | RequestID: 1752812195190966 | Status: 200 | Duration: 20.00ms
2025-07-18 12:55:07,482 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-18 12:55:07,505 [INFO] [SUCCESS] Flask应用初始化完成
2025-07-18 13:43:20,863 [INFO] [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-18 13:43:20,879 [INFO] [SUCCESS] Flask应用初始化完成
2025-07-18 13:43:39,623 [INFO] [API_REQUEST] POST api.get_freq_am | RequestID: 1752817419623157 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "EQ123456", "data_path": "csv_data/0001.csv"}
2025-07-18 13:43:39,623 [WARNING] 频率幅值请求参数错误: 缺少必要参数: rpm
2025-07-18 13:43:39,624 [INFO] [API_RESPONSE] POST api.get_freq_am | RequestID: 1752817419623157 | Status: 200 | Duration: 1.00ms
2025-07-18 13:43:53,897 [INFO] [API_REQUEST] POST api.get_freq_am | RequestID: 1752817433896406 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "EQ123456", "data_path": "csv_data/0001.csv"}
2025-07-18 13:43:53,897 [INFO] [API_CALL] freq_am | Equipment: EQ123456 | DataPath: csv_data/0001.csv | Rpm: 100 | Compress: False
2025-07-18 13:43:53,918 [INFO] [FAST_CSV] 文件: 0001.csv | 样本数: 12000 | 采样率: 12000.0Hz
2025-07-18 13:43:53,928 [ERROR] 频率幅值计算失败: 'EQ123456'
2025-07-18 13:43:53,929 [INFO] [API_RESPONSE] POST api.get_freq_am | RequestID: 1752817433896406 | Status: 200 | Duration: 33.50ms
2025-07-18 13:44:10,425 [INFO] [API_REQUEST] POST api.get_freq_am | RequestID: 1752817450424384 | IP: 127.0.0.1 | KeyParams: {"equipment_code": "EQ123456", "data_path": "csv_data/0001.csv"}
2025-07-18 13:44:10,425 [INFO] [API_CALL] freq_am | Equipment: EQ123456 | DataPath: csv_data/0001.csv | Rpm: 100 | Compress: False
2025-07-18 13:44:10,433 [INFO] [FAST_CSV] 文件: 0001.csv | 样本数: 12000 | 采样率: 12000.0Hz
2025-07-18 13:44:10,438 [ERROR] 频率幅值计算失败: 'EQ123456'
2025-07-18 13:44:10,439 [INFO] [API_RESPONSE] POST api.get_freq_am | RequestID: 1752817450424384 | Status: 200 | Duration: 14.78ms
2025-07-22 11:42:53,355 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-22 11:42:53,688 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成
2025-07-30 15:09:14,228 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-30 15:09:14,243 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成
2025-07-30 15:09:14,275 - vibmon_app.utils.config_loader - INFO - 成功加载设备配置: 10 个设备
2025-07-30 16:13:11,412 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-30 16:13:11,417 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成
2025-07-30 16:13:11,420 - vibmon_app.utils.minio_config - INFO - MinIO配置加载成功: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\config\minio.yaml
2025-07-30 16:13:11,421 - vibmon_app.utils.minio_config - INFO - MinIO配置验证通过
2025-07-30 16:13:11,425 - vibmon_app.utils.minio_config - INFO - MinIO配置加载成功: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\config\minio.yaml
2025-07-30 16:13:11,430 - vibmon_app.utils.minio_config - INFO - MinIO配置验证通过
2025-07-30 16:13:11,430 - vibmon_app.utils.minio_client - INFO - MinIO客户端连接成功: 47.122.157.161:9000
2025-07-30 16:13:11,433 - vibmon_app.utils.minio_config - INFO - MinIO配置加载成功: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\config\minio.yaml
2025-07-30 16:13:11,434 - vibmon_app.utils.minio_config - INFO - MinIO配置验证通过
2025-07-30 16:13:11,435 - vibmon_app.utils.csv_formatter - INFO - 开始格式化freq_am结果为CSV
2025-07-30 16:13:11,436 - vibmon_app.utils.csv_formatter - INFO - freq_am结果格式化完成，数据形状: (3, 14)
2025-07-30 16:13:11,437 - vibmon_app.utils.csv_formatter - INFO - 开始格式化multi_features结果为CSV
2025-07-30 16:13:11,437 - vibmon_app.utils.csv_formatter - INFO - multi_features结果格式化完成，数据形状: (1, 2)
2025-07-30 16:13:11,438 - vibmon_app.utils.csv_formatter - INFO - 特征列数: 2
2025-07-30 16:15:11,469 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-30 16:15:11,486 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成
2025-07-30 19:36:21,196 - vibmon_app - INFO - [SUCCESS] 成功加载SVM模型: C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\predictive_model\svm_anomaly_detection_model.pkl
2025-07-30 19:36:21,200 - vibmon_app - INFO - [SUCCESS] Flask应用初始化完成
2025-07-30 19:36:21,208 - vibmon_app.utils.config_loader - INFO - 成功加载设备配置: 10 个设备
