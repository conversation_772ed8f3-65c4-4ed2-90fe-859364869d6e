2025-07-16 10:12:11,644 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:11,644 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:11,644 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:11,644 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:11,644 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:11,644 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:11,644 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:11,644 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:11,644 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:11,644 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:12,154 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:12,154 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:12,154 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:12,154 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:12,154 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:12,154 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:12,154 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:12,154 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:12,154 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:12,154 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-16 10:12:12,154 - vibmon_app - ERROR - 频率幅值计算未知错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0) [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:181 in get_freq_am()]
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 598, in get_json
    rv = self.json_module.loads(data)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\json\provider.py", line 255, in loads
    return json.loads(s, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py", line 66, in get_freq_am
    json_data = request.get_json() if request.is_json else {}
                ^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 607, in get_json
    rv = self.on_json_loading_failed(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\flask\wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\anaconda3\Lib\site-packages\werkzeug\wrappers\request.py", line 629, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
2025-07-17 14:14:43,291 - vibmon_app - ERROR - 频率幅值计算失败: 文件不存在: csv_data/0002.csv [C:\Users\<USER>\Desktop\PHD\flaskdemo\vibmon\vibmon_app\api\routes.py:230 in get_freq_am()]
2025-07-18 13:43:53,928 [ERROR] 频率幅值计算失败: 'EQ123456'
2025-07-18 13:44:10,438 [ERROR] 频率幅值计算失败: 'EQ123456'
