#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gunicorn配置文件 - 针对振动监测系统高频调用优化
"""

import multiprocessing
import os

# 基础配置
bind = "0.0.0.0:5000"
backlog = 2048

# 工作进程配置
# 针对高频调用（每5秒10次）优化
# 根据容器资源动态调整
cpu_count = multiprocessing.cpu_count()

def get_container_memory_gb():
    """智能获取容器内存限制 (GB)"""

    # 1. 优先使用环境变量 (Docker Compose设置)
    env_memory = os.environ.get('CONTAINER_MEMORY_GB')
    if env_memory:
        try:
            return float(env_memory)
        except:
            pass

    # 2. 尝试从Docker cgroup获取内存限制
    try:
        with open('/sys/fs/cgroup/memory/memory.limit_in_bytes', 'r') as f:
            limit_bytes = int(f.read().strip())

        # 检查是否有实际限制 (不是默认的巨大值)
        if limit_bytes < 9223372036854775807:
            return limit_bytes / (1024**3)
    except:
        pass

    # 3. 使用系统内存作为fallback
    try:
        import psutil
        memory = psutil.virtual_memory()
        system_memory_gb = memory.total / (1024**3)
        # 为系统保留一些内存，使用80%
        return system_memory_gb * 0.8
    except:
        pass

    # 4. 最后的默认值
    return 4.0

container_memory_gb = get_container_memory_gb()

# 显示检测到的资源
print(f"🔍 资源检测: CPU={cpu_count}核, 内存={container_memory_gb:.1f}GB")

# 低资源配置优化（2核2GB）
if cpu_count <= 2 and container_memory_gb <= 2:
    workers = 1  # 单worker避免内存竞争
    worker_class = "sync"
    worker_connections = 500
    max_requests = 500  # 更频繁重启释放内存
    max_requests_jitter = 25
    print(f"🔧 低资源模式: {workers} worker, {container_memory_gb}GB内存")
# 中等资源配置（4核4GB+）
elif cpu_count <= 4 and container_memory_gb <= 4:
    workers = 2  # 2个worker平衡性能和内存
    worker_class = "sync"
    worker_connections = 800
    max_requests = 800
    max_requests_jitter = 40
    print(f"🔧 中等资源模式: {workers} workers, {container_memory_gb}GB内存")
# 高资源配置（8核8GB+）
else:
    workers = min(cpu_count * 2, 8)  # 最多8个worker
    worker_class = "sync"
    worker_connections = 1000
    max_requests = 1000
    max_requests_jitter = 50
    print(f"🔧 高资源模式: {workers} workers, {container_memory_gb}GB内存")

# 超时配置
timeout = 120  # 请求超时时间（秒）
keepalive = 5  # Keep-Alive连接超时
graceful_timeout = 30  # 优雅关闭超时

# 内存和性能优化
preload_app = True  # 预加载应用，减少内存占用
worker_tmp_dir = "/dev/shm"  # 使用内存文件系统作为临时目录

# 日志配置
loglevel = os.environ.get('GUNICORN_LOG_LEVEL', 'info')
accesslog = "logs/gunicorn_access.log"
errorlog = "logs/gunicorn_error.log"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 进程名称
proc_name = "vibmon-api"

# 用户和组（生产环境建议使用非root用户）
# user = "appuser"
# group = "appuser"

# 安全配置
limit_request_line = 4096
limit_request_fields = 100
limit_request_field_size = 8190

# 性能调优
def when_ready(server):
    """服务器启动完成时的回调"""
    server.log.info("振动监测API服务器启动完成")
    server.log.info(f"工作进程数: {workers}")
    server.log.info(f"监听地址: {bind}")

def worker_int(worker):
    """工作进程中断时的回调"""
    worker.log.info(f"工作进程 {worker.pid} 收到中断信号")

def pre_fork(server, worker):
    """工作进程fork前的回调"""
    server.log.info(f"正在启动工作进程 {worker.pid}")

def post_fork(server, worker):
    """工作进程fork后的回调"""
    server.log.info(f"工作进程 {worker.pid} 启动完成")

def pre_exec(server):
    """执行前的回调"""
    server.log.info("服务器正在重新加载")

def on_exit(server):
    """服务器退出时的回调"""
    server.log.info("振动监测API服务器正在关闭")

def on_reload(server):
    """服务器重载时的回调"""
    server.log.info("振动监测API服务器正在重载")

# 环境变量配置
raw_env = [
    'FLASK_ENV=production',
    'VIBMON_LOG_LEVEL=MINIMAL',
    'PYTHONUNBUFFERED=1',
    'ENABLE_DATA_COMPRESSION=true'
]

# 开发环境配置
if os.environ.get('FLASK_ENV') == 'development':
    workers = 2
    reload = True
    loglevel = 'debug'
    timeout = 300
    raw_env = [
        'FLASK_ENV=development',
        'VIBMON_LOG_LEVEL=DETAILED',
        'PYTHONUNBUFFERED=1'
    ]

# 监控环境配置
elif os.environ.get('FLASK_ENV') == 'monitoring':
    workers = min(multiprocessing.cpu_count(), 4)
    loglevel = 'info'
    raw_env = [
        'FLASK_ENV=production',
        'VIBMON_LOG_LEVEL=STANDARD',
        'PYTHONUNBUFFERED=1',
        'ENABLE_PERFORMANCE_MONITORING=true'
    ]
