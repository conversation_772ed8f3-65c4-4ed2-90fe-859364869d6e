# 最终统一版本 - Python 3.10.16 兼容
FROM python:3.10-slim

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONWARNINGS="ignore::DeprecationWarning"
ENV SUPPRESS_NUMPY_WARNINGS="true"
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8
ENV FLASK_ENV=production
ENV FLASK_DEBUG=False
ENV VIBMON_LOG_LEVEL=MINIMAL

# 设置工作目录
WORKDIR /app

# 更换为国内镜像源并安装系统依赖（加速下载）
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources \
    && sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources \
    && apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 复制依赖文件并安装（使用实际环境测试的版本）
COPY requirements.txt ./
RUN pip install --no-cache-dir --upgrade pip  -i https://pypi.tuna.tsinghua.edu.cn/simple\
    && pip install --no-cache-dir -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple\
    || (pip install --no-cache-dir -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple)

# 复制项目文件
COPY . .

# 创建必要的目录
RUN mkdir -p logs\
    && chmod 755 logs

# 创建非root用户 - 安全最佳实践
#RUN useradd -m -u 1000 appuser \
#    && chown -R appuser:appuser /app
#USER appuser

# 暴露端口
EXPOSE 5000

# 健康检查 - 防止容器假死
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# 启动命令 - 使用统一配置文件
# gunicorn_config.py 会根据环境变量自动调整资源配置
CMD ["gunicorn", "--config", "gunicorn_config.py", "run:app"]