# 振动监测系统 - 智能维护建议功能说明

**版本**: v2.2.0 Final | **Python**: 3.10.16 | **更新日期**: 2025-08-01

## 1. 功能概述

### 1.1 功能介绍
智能维护建议功能是振动监测系统v2.2.0的重要新增功能，基于设备健康度评估结果，使用科学的数学模型预测设备的最佳维护时间，为设备管理提供智能化的维护决策支持。

### 1.2 核心价值
- **预测性维护**：从被动维护转向主动预测性维护
- **成本优化**：避免过度维护和设备突发故障
- **风险控制**：及时识别需要紧急维护的设备
- **资源配置**：合理安排维护计划和资源分配

## 2. 技术原理

### 2.1 算法模型
采用**Sigmoid函数模型**进行维护时间预测：

```python
def calculate_remaining_life(health_degree, growth_rate=0.25, midpoint=79, mtce_period):
    """
    计算剩余使用寿命
    
    参数:
        health_degree: 健康度 (0-100)
        growth_rate: 增长率 (默认0.25)
        midpoint: 中点值 (默认79)
        mtce_period: 维护周期 (天)
    
    返回:
        剩余维护时间 (天)
    """
    health = np.clip(health_degree, 0, 100)
    x = 100 - health
    remaining_useful_life = mtce_period / (1 + np.exp(growth_rate * (x - (100 - midpoint)))) - 1
    return round(remaining_useful_life)
```

### 2.2 模型特点
- **非线性预测**：健康度下降时，维护建议时间非线性减少
- **边界安全**：自动处理0-100范围外的健康度值
- **可配置性**：支持不同维护周期的设备类型
- **科学性**：基于设备退化规律的数学模型

### 2.3 预测曲线
```
维护建议时间 (天)
    365 |     ●
        |      ●
    300 |       ●
        |        ●
    200 |         ●●
        |           ●●
    100 |             ●●●
        |                ●●●
      0 |___________________●●●
        0   20  40  60  80  100
              健康度 (%)
```

## 3. 接口使用

### 3.1 接口地址
```
POST /api/features/multi_features
```

### 3.2 请求参数
```json
{
    "equipment_code": "CAAQ000000497",
    "minio_url": "http://**************:9000/test1/data.csv",
    "health_degree_flag": 1,
    "predict_flag": 1,
    "early_warning_status": 0,
    "alarm_status": 0,
    "mtce_period": 365
}
```

**新增参数说明**：
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| mtce_period | int | 否 | 365 | 维护周期（天），支持自定义设备维护周期 |

### 3.3 响应结果
```json
{
    "status": "success",
    "timestamp": "2025-07-31T19:36:23.358744",
    "result": {
        "equipment_code": "CAAQ000000497",
        "rms_values": { /* RMS值 */ },
        "peak_to_peak": { /* 峰峰值 */ },
        "predict_result": "normal",
        "health_degree": 85.6,
        "mtce_advice": 204
    }
}
```

**新增字段说明**：
| 字段名 | 类型 | 说明 |
|--------|------|------|
| mtce_advice | int/null | 建议维护时间（天），当health_degree_flag=1时返回 |

## 4. 使用场景

### 4.1 设备健康度与维护建议对应关系

| 健康度范围 | 设备状态 | 维护建议时间 | 维护优先级 | 建议措施 |
|------------|----------|--------------|------------|----------|
| 90-100% | 优秀 | 350-365天 | 低 | 按计划维护 |
| 80-89% | 良好 | 180-350天 | 中 | 关注监测 |
| 60-79% | 一般 | 60-180天 | 高 | 计划维护 |
| 40-59% | 较差 | 20-60天 | 很高 | 尽快维护 |
| 0-39% | 危险 | 1-20天 | 紧急 | 立即维护 |

### 4.2 典型应用场景

#### 场景1：日常监测
```bash
# 定期检查设备健康状态
curl -X POST http://localhost:5000/api/features/multi_features \
  -H "Content-Type: application/json" \
  -d '{
    "equipment_code": "PUMP001",
    "minio_url": "http://minio:9000/data/pump001_daily.csv",
    "health_degree_flag": 1,
    "predict_flag": 1,
    "mtce_period": 365
  }'

# 响应示例
{
  "result": {
    "health_degree": 78.5,
    "mtce_advice": 156,  // 建议156天后维护
    "predict_result": "normal"
  }
}
```

#### 场景2：紧急评估
```bash
# 设备异常后的紧急评估
curl -X POST http://localhost:5000/api/features/multi_features \
  -H "Content-Type: application/json" \
  -d '{
    "equipment_code": "MOTOR002",
    "minio_url": "http://minio:9000/data/motor002_emergency.csv",
    "health_degree_flag": 1,
    "predict_flag": 1,
    "mtce_period": 180
  }'

# 响应示例
{
  "result": {
    "health_degree": 35.2,
    "mtce_advice": 8,    // 建议8天内维护
    "predict_result": "abnormal"
  }
}
```

#### 场景3：不同设备类型
```bash
# 高频维护设备（如关键泵）
{
  "mtce_period": 90,     // 90天维护周期
  "health_degree": 85,
  "mtce_advice": 51      // 建议51天后维护
}

# 低频维护设备（如备用设备）
{
  "mtce_period": 730,    // 2年维护周期
  "health_degree": 85,
  "mtce_advice": 408     // 建议408天后维护
}
```

## 5. 配置说明

### 5.1 维护周期配置
不同类型设备的推荐维护周期：

| 设备类型 | 推荐周期 | 说明 |
|----------|----------|------|
| 关键泵类 | 90-180天 | 高负荷运行设备 |
| 电机类 | 180-365天 | 标准工业电机 |
| 风机类 | 365-730天 | 大型风机设备 |
| 压缩机 | 180-365天 | 压缩机设备 |
| 备用设备 | 730-1095天 | 低频使用设备 |

### 5.2 算法参数调优
```python
# 默认参数（适用于大多数设备）
growth_rate = 0.25    # 控制曲线陡峭程度
midpoint = 79         # 健康度中点值

# 敏感设备参数（更保守的维护建议）
growth_rate = 0.35    # 更陡峭的曲线
midpoint = 85         # 更高的中点值

# 稳定设备参数（更宽松的维护建议）
growth_rate = 0.15    # 更平缓的曲线
midpoint = 70         # 更低的中点值
```

## 6. 最佳实践

### 6.1 维护计划制定
1. **定期监测**：每周或每月进行健康度评估
2. **趋势分析**：记录健康度变化趋势
3. **提前规划**：根据维护建议提前安排资源
4. **应急响应**：建立紧急维护流程

### 6.2 数据质量保证
1. **数据完整性**：确保振动数据采集完整
2. **采样频率**：保持一致的采样频率
3. **环境因素**：考虑温度、负载等环境因素
4. **标定校准**：定期校准传感器

### 6.3 维护决策流程
```
数据采集 → 健康度评估 → 维护建议 → 风险评估 → 维护决策 → 执行维护 → 效果验证
```

## 7. 注意事项

### 7.1 使用限制
- 维护建议仅在`health_degree_flag=1`时计算
- 健康度为0或无法计算时，返回`null`
- 建议时间基于当前健康度，不考虑历史趋势

### 7.2 安全提醒
- 维护建议仅供参考，不能替代专业判断
- 紧急情况下应立即停机检查
- 建议结合其他监测手段综合判断

### 7.3 技术支持
- 算法参数可根据实际情况调整
- 支持自定义维护周期配置
- 提供完整的API文档和技术支持

---

**智能维护建议功能为设备管理提供了科学的决策支持，帮助实现从被动维护向预测性维护的转变。**
