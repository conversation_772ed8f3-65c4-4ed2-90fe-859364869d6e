# 振动监测系统 - Docker部署文档

**版本**: v2.2.0 Final | **Python**: 3.10.16 | **更新日期**: 2025-08-01

## 🚀 概述

本系统采用**统一Docker配置**，支持多种资源配置模式，可根据服务器配置自动调整性能参数。系统采用Flask + Gunicorn架构，针对高频API调用进行了深度优化。

### 核心特性
- **多资源配置模式**：低资源(2核2GB)、中等资源(4核4GB)、高资源(8核8GB+)
- **统一部署脚本**：一键部署，支持自定义配置
- **动态资源调整**：根据容器资源自动优化Gunicorn配置
- **智能数据压缩**：可选压缩减少75%传输量
- **实数FFT算法**：性能提升约2倍
- **轴承特征频率分析**：专业的振动分析功能

## 1. 部署前准备

### 1.1 系统要求
- **操作系统**：Linux/Windows/macOS（推荐Linux生产环境）
- **Docker版本**：20.10.0 或更高版本
- **内存要求**：最低2GB，推荐4GB以上（高频调用场景）
- **CPU要求**：最低2核，推荐4核以上
- **磁盘空间**：最低3GB可用空间
- **网络要求**：能够访问Docker Hub和Python包管理器

### 1.2 环境检查
在开始部署前，请确认以下环境：

**检查Docker安装**：
```bash
docker --version
# 预期输出：Docker version 20.10.0 或更高
```

**检查Docker服务状态**：
```bash
# Linux/macOS
sudo systemctl status docker

# Windows
# 确认Docker Desktop正在运行
```

**检查系统资源**：
```bash
# 检查可用内存（推荐4GB+）
free -h

# 检查CPU核数（推荐4核+）
nproc

# 检查磁盘空间
df -h
```

### 1.3 项目文件检查
确保以下核心文件存在：
- `Dockerfile` - Docker构建文件
- `gunicorn_config.py` - 动态Gunicorn配置
- `deploy.sh` - 统一部署脚本
- `requirements.txt` - Python依赖包
- `vibmon_app/` - 应用代码目录

## 2. 快速部署

### 2.1 一键部署（推荐）

**使用Docker Compose部署脚本**：
```bash
# 1. 进入项目目录
cd /path/to/vibmon

# 2. 开发模式部署（2核2GB）
./deploy-compose.sh dev

# 3. 生产模式部署（4核4GB）
./deploy-compose.sh prod

# 4. 自定义配置部署
./deploy-compose.sh custom

# 5. 强制重新构建部署
./deploy-compose.sh prod --build

# 6. 清理环境后部署
./deploy-compose.sh dev --clean --build
```

**Windows用户**：
```bash
# 使用Git Bash或WSL运行
bash deploy-compose.sh dev
```

**快速启动脚本**：
```bash
# Linux/Mac
./docker-start.sh

# Windows
docker-start.bat
```

**注意**: 系统使用Docker Compose管理，自动配置Gunicorn和资源限制。

### 2.2 资源配置模式

系统支持三种预定义的资源配置模式：

**低资源模式（2核2GB）**：
- 适用场景：开发测试、小规模部署
- Worker进程：1个
- 内存限制：2GB
- CPU限制：2.0核

**中等资源模式（4核4GB）**：
- 适用场景：中等负载生产环境
- Worker进程：2个
- 内存限制：4GB
- CPU限制：4.0核

**高资源模式（8核8GB+）**：
- 适用场景：高频调用、大规模部署
- Worker进程：最多8个
- 内存限制：8GB+
- CPU限制：8.0核+

### 2.3 手动Docker Compose部署

**步骤1：使用默认配置**
```bash
# 构建并启动（开发模式）
docker-compose up -d --build
```

**步骤2：使用生产配置**
```bash
# 使用生产环境配置文件
docker-compose -f docker-compose.prod.yml up -d --build
```

**步骤3：自定义资源配置**
```bash
# 设置环境变量后启动
export CONTAINER_MEMORY_GB=8
export CONTAINER_CPU_COUNT=8
export GUNICORN_WORKERS=16
docker-compose up -d --build
```

### 2.4 验证部署
```bash
# 检查容器状态
docker-compose ps

# 检查健康状态
curl http://localhost:5000/health

# 检查API状态
curl http://localhost:5000/api/config/info

# 查询可用设备
curl http://localhost:5000/api/devices

# 功能测试 - 频率幅值分析
curl -X POST http://localhost:5000/api/features/freq_am \
  -H "Content-Type: application/json" \
  -d '{
    "equipment_code":"CAAQ000000497",
    "minio_url":"http://**************:9000/test1/data/input.csv",
    "rpm":1800,
    "save_to_minio":true
  }'

# 功能测试 - 多特征分析（含维护建议）
curl -X POST http://localhost:5000/api/features/multi_features \
  -H "Content-Type: application/json" \
  -d '{
    "equipment_code":"CAAQ000000497",
    "minio_url":"http://**************:9000/test1/data/input.csv",
    "health_degree_flag":1,
    "predict_flag":1,
    "mtce_period":365
  }'
```

## 3. 配置说明

### 3.1 Gunicorn高性能配置
系统内置 `gunicorn_config.py` 配置文件，针对高频调用优化：

**核心配置**：
```python
# 工作进程配置
workers = min(multiprocessing.cpu_count() * 2, 8)  # 最多8个worker
worker_class = "sync"  # 适合CPU密集型任务
timeout = 120  # 请求超时时间
preload_app = True  # 预加载应用，减少内存占用

# 性能优化
max_requests = 1000  # 每个worker处理1000个请求后重启
max_requests_jitter = 50  # 随机化重启，避免同时重启
worker_tmp_dir = "/dev/shm"  # 使用内存文件系统
```

### 3.2 环境变量配置
可以通过环境变量自定义配置：

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| FLASK_ENV | production | Flask运行环境 |
| VIBMON_LOG_LEVEL | MINIMAL | 日志级别（MINIMAL/STANDARD/DETAILED） |
| GUNICORN_WORKERS | auto | Gunicorn工作进程数 |
| GUNICORN_TIMEOUT | 120 | 请求超时时间（秒） |
| ENABLE_DATA_COMPRESSION | true | 是否启用数据压缩 |
| MINIO_ENDPOINT | - | MinIO服务器地址（必需） |
| MINIO_ACCESS_KEY | - | MinIO访问密钥（必需） |
| MINIO_SECRET_KEY | - | MinIO密钥（必需） |
| MINIO_INPUT_BUCKET | input-bucket | MinIO输入存储桶 |
| MINIO_OUTPUT_BUCKET | output-bucket | MinIO输出存储桶 |
| CONTAINER_MEMORY_GB | auto | 容器内存限制（GB） |
| CONTAINER_CPU_COUNT | auto | 容器CPU核数 |

**自定义环境变量示例**：
```bash
docker run -d \
  --name vibmon-api \
  -p 5000:5000 \
  -e VIBMON_LOG_LEVEL=STANDARD \
  -e GUNICORN_WORKERS=6 \
  -e GUNICORN_TIMEOUT=180 \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/csv_data:/app/csv_data \
  vibmon-api:latest
```

### 3.3 端口配置
默认端口映射：
- **主机端口**：5000
- **容器端口**：5000
- **访问地址**：http://localhost:5000

**修改端口示例**：
```bash
# 映射到8080端口
docker run -d \
  --name vibmon-api \
  -p 8080:5000 \
  vibmon-api:latest

# 访问地址变为：http://localhost:8080
```

### 3.4 资源配置对比

| 配置级别 | 内存 | CPU | Worker数 | 适用场景 | 并发能力 |
|---------|------|-----|---------|---------|---------|
| 轻量 | 2GB | 2核 | 4 | 测试、演示 | 低-中等 |
| 标准 | 4GB | 4核 | 8 | 生产环境 | 中等-高 |
| 高性能 | 8GB | 8核 | 16 | 高负载 | 高 |

## 4. 部署验证

### 4.1 服务状态检查
```bash
# 检查容器运行状态
docker ps

# 预期输出：
# CONTAINER ID   IMAGE              COMMAND                  STATUS      PORTS
# abc123def456   vibmon-api:latest  "gunicorn --config g…"   Up 2 mins   0.0.0.0:5000->5000/tcp
```

### 4.2 健康检查
```bash
# 检查服务健康状态
curl http://localhost:5000/health

# 预期输出：
# {"status":"healthy","service":"vibration-monitoring"}
```

### 4.3 API功能测试
```bash
# 检查API状态
curl http://localhost:5000/api/status

# 预期输出：
# {
#   "status": "running",
#   "model_loaded": true,
#   "timestamp": "2024-01-01T12:00:00.000Z",
#   "version": "2.2.0"
# }
```

### 4.4 性能测试
```bash
# 测试freq_am接口
curl -X POST http://localhost:5000/api/features/freq_am \
  -H "Content-Type: application/json" \
  -d '{
    "equipment_code": "CAAQ000000497",
    "minio_url": "http://**************:9000/test1/data/input.csv",
    "rpm": 1800,
    "save_to_minio": true
  }'

# 测试维护建议功能
curl -X POST http://localhost:5000/api/features/multi_features \
  -H "Content-Type: application/json" \
  -d '{
    "equipment_code": "CAAQ000000497",
    "minio_url": "http://**************:9000/test1/data/input.csv",
    "health_degree_flag": 1,
    "predict_flag": 1,
    "mtce_period": 365
  }'

# 预期响应包含performance字段和维护建议：
# {
#   "status": "success",
#   "result": {
#     "mtce_advice": 182,  // 建议维护时间（天）
#     "health_degree": 85.5,
#     ...
#   },
#   "performance": {
#     "total_time_ms": 1500.25,
#     "load_time_ms": 200.15,
#     "compute_time_ms": 1200.10,
#     "data_points": 4000,
#     "compression_enabled": true
#   }
# }
```

### 4.5 维护建议功能验证
```bash
# 测试维护建议功能
curl -X POST http://localhost:5000/api/features/multi_features \
  -H "Content-Type: application/json" \
  -d '{
    "equipment_code": "TEST001",
    "minio_url": "http://**************:9000/test1/data/input.csv",
    "health_degree_flag": 1,
    "predict_flag": 1,
    "early_warning_status": 0,
    "alarm_status": 0,
    "mtce_period": 365
  }'

# 预期响应包含维护建议字段：
# {
#   "status": "success",
#   "result": {
#     "health_degree": 85.5,
#     "mtce_advice": 182,  // 建议维护时间（天）
#     "predict_result": {...},
#     "rms_values": {...},
#     ...
#   },
#   "performance": {...}
# }

# 验证不同健康度的维护建议
# 健康度90+ -> 维护建议350+天
# 健康度80-89 -> 维护建议180-350天
# 健康度60-79 -> 维护建议60-180天
# 健康度40-59 -> 维护建议20-60天
# 健康度<40 -> 维护建议1-20天
```

### 4.6 日志检查
```bash
# 查看实时日志
docker logs -f vibmon-api

# 查看最近100行日志
docker logs --tail=100 vibmon-api

# 查看Gunicorn访问日志
docker exec vibmon-api tail -f /app/logs/gunicorn_access.log

# 查看Gunicorn错误日志
docker exec vibmon-api tail -f /app/logs/gunicorn_error.log
```

## 5. 运维管理

### 5.1 服务管理命令
```bash
# 启动服务
docker start vibmon-api

# 停止服务
docker stop vibmon-api

# 重启服务
docker restart vibmon-api

# 重新构建并启动
docker-compose down && \
docker-compose up -d --build

# 查看服务状态
docker-compose ps

# 查看资源使用情况
docker-compose exec vibmon-api top

# 查看日志
docker-compose logs -f
```

### 5.2 数据管理
```bash
# 备份数据目录
tar -czf vibmon_data_backup_$(date +%Y%m%d).tar.gz csv_data/ logs/

# 清理日志文件（保留7天）
docker exec vibmon-api find /app/logs -name "*.log" -mtime +7 -delete

# 查看磁盘使用情况
docker exec vibmon-api df -h

# 查看日志文件大小
docker exec vibmon-api du -sh /app/logs/*
```

### 5.3 性能监控
```bash
# 查看容器资源使用（实时）
docker stats vibmon-api

# 查看Gunicorn进程状态
docker exec vibmon-api ps aux | grep gunicorn

# 查看内存使用详情
docker exec vibmon-api cat /proc/meminfo | head -10

# 查看CPU使用情况
docker exec vibmon-api top -bn1 | head -20

# 监控API响应时间
curl -w "@curl-format.txt" -o /dev/null -s \
  -X POST http://localhost:5000/api/features/freq_am \
  -H "Content-Type: application/json" \
  -d '{"equipment_code":"CAAQ000000497","minio_url":"http://**************:9000/test1/data/input.csv","rpm":1800}'
```

### 5.4 性能调优
```bash
# 动态调整容器资源限制
docker update --memory=8g --cpus=8.0 vibmon-api

# 查看Gunicorn配置
docker exec vibmon-api cat /app/gunicorn_config.py

# 重载Gunicorn配置（无需重启）
docker exec vibmon-api kill -HUP 1
```

## 6. 故障排除

### 6.1 常见问题及解决方案

**问题1：容器启动失败**
```bash
# 查看详细错误信息
docker logs vibmon-api

# 常见原因及解决方案：
# - 端口被占用：修改端口映射 -p 8080:5000
# - 依赖安装失败：检查网络连接，重新构建镜像
# - 权限问题：确保Docker有足够权限访问项目目录
# - Gunicorn配置错误：检查gunicorn_config.py文件
```

**问题2：服务无法访问**
```bash
# 检查端口是否正确映射
docker port vibmon-api

# 检查防火墙设置
# Linux: sudo ufw status
# Windows: 检查Windows防火墙设置

# 检查服务是否在容器内正常运行
docker exec vibmon-api curl http://localhost:5000/health

# 检查Gunicorn进程状态
docker exec vibmon-api ps aux | grep gunicorn
```

**问题3：性能问题（响应慢）**
```bash
# 检查资源使用情况
docker stats vibmon-api

# 检查Gunicorn worker数量
docker exec vibmon-api ps aux | grep gunicorn | wc -l

# 增加资源限制
docker update --memory=8g --cpus=8.0 vibmon-api

# 重启容器应用新配置
docker restart vibmon-api
```

**问题4：内存不足**
```bash
# 查看内存使用情况
docker stats vibmon-api

# 查看详细内存信息
docker exec vibmon-api cat /proc/meminfo | head -5

# 动态调整内存限制
docker update --memory=8g vibmon-api

# 重启服务
docker restart vibmon-api
```

**问题5：模型加载失败**
```bash
# 检查模型文件是否存在
docker exec vibmon-api ls -la /app/predictive_model/

# 检查文件权限
docker exec vibmon-api ls -la /app/predictive_model/svm_anomaly_detection_model.pkl

# 重新启动服务
docker restart vibmon-api
```

### 6.2 日志分析
```bash
# 查看应用错误日志
docker logs vibmon-api | grep ERROR

# 查看Gunicorn访问日志
docker exec vibmon-api tail -f /app/logs/gunicorn_access.log

# 查看Gunicorn错误日志
docker exec vibmon-api tail -f /app/logs/gunicorn_error.log

# 查看性能相关日志
docker logs vibmon-api | grep PERFORMANCE

# 导出日志到文件
docker logs vibmon-api > vibmon_logs_$(date +%Y%m%d).log

# 分析慢请求
docker exec vibmon-api grep "total_time_ms.*[5-9][0-9][0-9][0-9]" /app/logs/gunicorn_access.log
```

### 6.3 性能优化建议
```bash
# 清理未使用的Docker资源
docker system prune -f

# 清理未使用的镜像
docker image prune -f

# 清理未使用的数据卷
docker volume prune -f

# 优化容器配置
# 1. 增加worker数量（CPU密集型任务）
# 2. 调整内存限制（建议4GB+）
# 3. 使用SSD存储提升I/O性能
# 4. 启用数据压缩减少网络传输

# 监控关键指标
# - 响应时间：目标 < 5秒
# - CPU使用率：目标 < 80%
# - 内存使用率：目标 < 70%
# - 并发处理能力：目标 > 10 QPS
```

## 7. 安全配置

### 7.1 容器安全
```bash
# 使用非root用户运行（可选，已在Dockerfile中配置）
# 限制容器权限
docker run -d \
  --name vibmon-api \
  --security-opt no-new-privileges \
  --read-only \
  --tmpfs /tmp \
  --tmpfs /var/run \
  -p 5000:5000 \
  vibmon-api:latest
```

### 7.2 网络安全
```bash
# 创建自定义网络
docker network create --driver bridge vibmon-network

# 在自定义网络中运行容器
docker run -d \
  --name vibmon-api \
  --network vibmon-network \
  -p 5000:5000 \
  vibmon-api:latest
```

### 7.3 数据安全
```bash
# 设置MinIO连接配置
docker run -d \
  --name vibmon-api \
  -p 5000:5000 \
  -e MINIO_ENDPOINT="http://your-minio:9000" \
  -e MINIO_ACCESS_KEY="your-access-key" \
  -e MINIO_SECRET_KEY="your-secret-key" \
  -v $(pwd)/logs:/app/logs \
  vibmon-api:latest
```

## 8. 生产环境部署

### 8.1 高可用部署
```bash
# 部署多个实例实现高可用
for i in {1..3}; do
  docker run -d \
    --name vibmon-api-$i \
    -p $((5000+$i-1)):5000 \
    -v $(pwd)/logs:/app/logs \
    -v $(pwd)/csv_data:/app/csv_data \
    --restart unless-stopped \
    --memory=4g \
    --cpus=4.0 \
    vibmon-api:latest
done
```

### 8.2 负载均衡配置
```bash
# 使用Nginx作为负载均衡器
docker run -d \
  --name nginx-lb \
  -p 80:80 \
  -v $(pwd)/nginx.conf:/etc/nginx/nginx.conf \
  --link vibmon-api-1 \
  --link vibmon-api-2 \
  --link vibmon-api-3 \
  nginx:alpine
```

### 8.3 监控部署
```bash
# 部署监控容器
docker run -d \
  --name vibmon-monitor \
  -p 9090:9090 \
  -v $(pwd)/prometheus.yml:/etc/prometheus/prometheus.yml \
  prom/prometheus

# 部署Grafana仪表板
docker run -d \
  --name grafana \
  -p 3000:3000 \
  grafana/grafana
```

## 9. 备份与恢复

### 9.1 数据备份
```bash
# 创建完整备份
docker exec vibmon-api tar -czf /tmp/backup.tar.gz /app/csv_data /app/logs
docker cp vibmon-api:/tmp/backup.tar.gz ./backup_$(date +%Y%m%d).tar.gz

# 备份容器镜像
docker save vibmon-api:latest | gzip > vibmon-api-image-$(date +%Y%m%d).tar.gz
```

### 9.2 数据恢复
```bash
# 恢复数据
docker cp ./backup_20240101.tar.gz vibmon-api:/tmp/
docker exec vibmon-api tar -xzf /tmp/backup_20240101.tar.gz -C /

# 恢复容器镜像
gunzip -c vibmon-api-image-20240101.tar.gz | docker load
```

### 9.3 自动备份脚本
```bash
#!/bin/bash
# backup.sh - 自动备份脚本
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/vibmon"
mkdir -p $BACKUP_DIR

# 备份数据
docker exec vibmon-api tar -czf /tmp/vibmon_backup_$DATE.tar.gz /app/csv_data /app/logs
docker cp vibmon-api:/tmp/vibmon_backup_$DATE.tar.gz $BACKUP_DIR/

# 备份镜像
docker save vibmon-api:latest | gzip > $BACKUP_DIR/vibmon-image_$DATE.tar.gz

# 清理旧备份（保留7天）
find $BACKUP_DIR -name "vibmon_backup_*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "vibmon-image_*.tar.gz" -mtime +7 -delete

echo "备份完成: $BACKUP_DIR/vibmon_backup_$DATE.tar.gz"
```

## 10. 性能基准测试

### 10.1 快速性能测试
```bash
# 下载测试脚本
curl -O https://raw.githubusercontent.com/your-repo/vibmon/main/quick_test.py

# 运行性能测试
python quick_test.py

# 预期结果：
# ✓ 成功请求: 10/10
# ✓ 平均响应时间: < 5000ms
# ✓ 数据压缩率: > 70%
```

### 10.2 压力测试
```bash
# 使用Apache Bench进行压力测试
ab -n 100 -c 10 -T 'application/json' \
   -p test_data.json \
   http://localhost:5000/api/features/freq_am

# 预期指标：
# - 并发10用户
# - 100个请求
# - 平均响应时间 < 5秒
# - 成功率 > 95%
```

## 📋 部署检查清单

- [ ] Docker环境已安装并运行
- [ ] 系统资源满足要求（4GB内存，4核CPU）
- [ ] 项目文件完整（Dockerfile, gunicorn_config.py等）
- [ ] 端口5000未被占用
- [ ] 数据目录已准备（csv_data/）
- [ ] 容器成功启动
- [ ] 健康检查通过
- [ ] API功能测试通过
- [ ] 性能测试满足要求
- [ ] 日志记录正常
- [ ] 备份策略已配置

## 🚀 快速命令参考

```bash
# 一键部署
./deploy-compose.sh dev          # 开发模式
./deploy-compose.sh prod         # 生产模式
./deploy-compose.sh custom       # 自定义模式

# 基础操作
docker-compose up -d --build     # 构建并启动
docker-compose down              # 停止服务
docker-compose restart          # 重启服务
docker-compose ps               # 查看状态

# 健康检查
curl http://localhost:5000/health

# 性能测试 - 频率幅值分析
curl -X POST http://localhost:5000/api/features/freq_am \
  -H "Content-Type: application/json" \
  -d '{"equipment_code":"CAAQ000000497","minio_url":"http://**************:9000/test1/data/input.csv","rpm":1800}'

# 性能测试 - 维护建议功能
curl -X POST http://localhost:5000/api/features/multi_features \
  -H "Content-Type: application/json" \
  -d '{"equipment_code":"CAAQ000000497","minio_url":"http://**************:9000/test1/data/input.csv","health_degree_flag":1,"predict_flag":1,"mtce_period":365}'

# 查看日志
docker-compose logs -f

# 监控资源
docker-compose exec vibmon-api top
docker stats $(docker-compose ps -q)

# 配置管理
vim config/devices.yaml                                    # 编辑配置
curl -X POST http://localhost:5000/api/config/reload      # 重载配置
```

## v2.2.0 更新说明

### 🆕 新增功能
- **智能维护建议**: multi_features接口新增基于健康度的维护时间预测
  - 新增`mtce_period`参数：自定义维护周期（天）
  - 新增`mtce_advice`响应字段：建议维护时间（天）
  - 使用Sigmoid函数模型，科学预测设备维护时间
- **配置覆盖优化**: 完善了环境变量覆盖机制
- **部署简化**: 移除了不必要的SECRET_KEY配置

### 🔧 配置变更
- **简化环境变量**: 不再需要配置SECRET_KEY
- **MinIO配置增强**: 支持完整的环境变量覆盖
- **资源检测改进**: 智能检测容器资源限制
- **维护建议算法**: 基于健康度的智能维护时间计算

### 📋 推荐的docker-compose.yml配置
```yaml
services:
  vibmon-app:
    image: your-registry/vibmon:v2.2.0
    environment:
      # MinIO配置 (必需)
      - MINIO_ENDPOINT=http://your-minio:9000
      - MINIO_ACCESS_KEY=your-access-key
      - MINIO_SECRET_KEY=your-secret-key
      - MINIO_INPUT_BUCKET=input-bucket
      - MINIO_OUTPUT_BUCKET=output-bucket

      # 应用配置 (可选)
      - FLASK_ENV=production
      - VIBMON_LOG_LEVEL=STANDARD

    volumes:
      - ./config:/app/config:ro
      - ./logs:/app/logs:rw

    ports:
      - "5000:5000"
```
