# 振动监测系统 - 项目总体介绍

## 1. 项目概述

### 1.1 项目简介
振动监测系统是一个基于Flask框架开发的高性能智能振动信号分析平台，专门用于工业设备的振动数据采集、处理和健康状态评估。系统采用现代化的微服务架构，支持Docker容器化部署，具备高可用性和可扩展性。

**最新版本**: v2.2.0 Final
**Python环境**: 3.10.16 兼容版本
**发布日期**: 2025-07-31

### 1.2 核心功能
- **振动信号特征提取**：提取RMS、峰峰值、均值、标准差、峭度等统计特征
- **包络谱分析**：进行频率域分析，提取关键频率和幅值信息
- **异常检测**：基于SVM机器学习模型的智能异常检测
- **健康度评估**：设备健康状态实时评估和预警
- **智能维护建议**：基于健康度的科学维护时间预测和建议
- **多轴数据处理**：支持水平、轴向、垂直三轴振动数据同时分析
- **批量数据处理**：支持CSV格式的批量振动数据处理
- **性能监控**：实时API性能统计和系统监控

### 1.3 技术优势
- **高性能处理**：Gunicorn多进程架构，支持高频数据采集（每5秒10次调用）
- **智能服务器选择**：Windows自动使用Waitress，Linux/macOS使用Gunicorn
- **性能优化**：实数FFT算法、智能数据压缩、向量化计算
- **智能分析**：集成机器学习模型，提供智能异常检测和预测
- **容器化部署**：Docker容器化，支持快速部署和扩展
- **RESTful API**：标准化API接口，易于集成和对接
- **实时监控**：支持实时数据处理和状态监控
- **跨平台兼容**：完美支持Windows、Linux、macOS

## 2. 系统架构

### 2.1 整体架构
```
振动监测系统 v2.0.0 Final
├── API接口层          # RESTful API服务 + 性能监控
├── 业务逻辑层          # 信号处理、特征提取、健康评估
├── 数据处理层          # 数据验证、格式转换、存储管理
├── 机器学习层          # SVM模型、异常检测、预测分析
├── 服务器层           # Gunicorn多进程 / Waitress多线程
└── 基础设施层          # 日志管理、配置管理、监控告警
```

### 2.2 技术栈
- **后端框架**：Flask 3.1.1
- **数据处理**：NumPy 1.24.3, SciPy 1.11.1, Pandas 2.0.3
- **机器学习**：Scikit-learn 1.5.0
- **Web服务器**：Gunicorn 21.2.0 (Linux/macOS) / Waitress 3.0.0 (Windows)
- **容器化**：Docker + Docker Compose
- **运行环境**：Python 3.10.16 (兼容版本)
- **数据格式**：CSV文件格式
- **API协议**：HTTP/HTTPS RESTful API

### 2.3 部署架构
```
客户端应用
    ↓ HTTP/HTTPS
负载均衡器（可选）
    ↓
Docker容器
├── 振动监测API服务
├── 日志管理
├── 数据存储
└── 健康检查
```

## 3. 项目目录结构

```
vibmon/                           # 项目根目录
├── 交付文档/                      # 项目交付文档
├── vibmon_app/                   # 主应用模块
│   ├── __init__.py              # Flask应用工厂
│   ├── config.py                # 配置管理
│   ├── api/                     # API接口层
│   │   ├── routes.py            # API路由定义
│   │   └── validators.py        # 请求参数验证
│   ├── services/                # 业务服务层
│   │   ├── signal_processing.py # 信号处理服务
│   │   ├── feature_extraction.py# 特征提取服务
│   │   └── health_assessment.py # 健康评估服务
│   ├── models/                  # 机器学习模型
│   │   └── ml_models.py         # 模型管理
│   └── utils/                   # 工具模块
│       ├── helpers.py           # 工具函数
│       ├── exceptions.py        # 自定义异常
│       └── logging_utils.py     # 日志工具
├── csv_data/                    # 测试数据目录
├── logs/                        # 日志文件目录
├── predictive_model/            # 预训练模型目录
├── tools/                       # 工具脚本
├── tests/                       # 测试代码
├── config/                      # 配置文件
├── run.py                       # 统一启动脚本（整合所有功能）
├── gunicorn_config.py           # Gunicorn动态配置（支持多资源模式）
├── requirements.txt             # Python依赖包（3.10.16兼容）
├── Dockerfile                   # Docker构建文件
├── deploy.sh                    # 统一部署脚本（支持多种资源配置）
├── healthcheck.py               # 健康检查脚本
├── test_performance.py          # 性能测试脚本
└── 交付文档/                     # 完整交付文档包
```

## 4. 核心特性

### 4.1 高可用性
- **健康检查**：内置健康检查机制，自动监控服务状态
- **容错处理**：完善的异常处理和错误恢复机制
- **资源管理**：智能资源分配和内存管理
- **重启策略**：自动重启机制，确保服务持续可用

### 4.2 性能优化
- **高频处理**：Gunicorn多进程架构，支持每5秒10次高频API调用
- **智能服务器**：Windows自动使用Waitress，Linux/macOS使用Gunicorn
- **算法优化**：实数FFT算法、向量化计算、智能数据压缩
- **内存优化**：高效的数据处理算法，降低内存占用
- **并发处理**：多进程/多线程并发请求处理
- **缓存机制**：智能缓存策略，提升响应速度

### 4.3 安全性
- **输入验证**：严格的输入参数验证和过滤
- **错误处理**：安全的错误信息返回，避免敏感信息泄露
- **资源限制**：容器资源限制，防止资源滥用
- **日志审计**：完整的操作日志记录

### 4.4 可维护性
- **模块化设计**：清晰的模块划分，便于维护和扩展
- **配置管理**：集中化配置管理，支持环境变量配置
- **日志管理**：分级日志记录，便于问题诊断
- **文档完善**：详细的API文档和操作手册

## 5. 应用场景

### 5.1 工业设备监控
- 旋转机械设备振动监测
- 生产线设备健康状态评估
- 设备故障预警和预测性维护

### 5.2 基础设施监控
- 桥梁、建筑物结构健康监测
- 风力发电机组振动分析
- 铁路、地铁设备状态监控

### 5.3 科研教学
- 振动信号分析研究
- 机器学习算法验证
- 工程教学实验平台

## 6. 系统优势

### 6.1 技术优势
- **成熟稳定**：基于成熟的开源技术栈，稳定可靠
- **标准化**：遵循RESTful API标准，易于集成
- **可扩展**：模块化架构，支持功能扩展和定制
- **跨平台**：Docker容器化，支持多平台部署

### 6.2 业务优势
- **快速部署**：一键Docker部署，快速上线
- **易于集成**：标准API接口，易于与现有系统集成
- **成本效益**：开源技术栈，降低技术成本
- **专业支持**：完善的文档和技术支持

### 6.3 用户优势
- **简单易用**：友好的API接口，降低使用门槛
- **实时响应**：快速的数据处理和结果返回
- **可靠稳定**：7×24小时稳定运行
- **持续优化**：持续的功能更新和性能优化

## 7. 版本更新历史

### v2.2.0 (2025-07-31) - 最新版本
**🆕 重大功能更新**：
- **智能维护建议系统**：新增基于健康度的科学维护时间预测
  - 支持自定义维护周期配置
  - 使用Sigmoid函数模型进行非线性预测
  - 提供精确的维护时间建议（天数）

**🔧 技术优化**：
- **配置管理增强**：完善环境变量覆盖机制，支持Docker部署配置
- **部署简化**：移除不必要的SECRET_KEY配置，简化部署流程
- **资源检测改进**：智能检测容器资源限制，自动优化性能配置

**🐛 Bug修复**：
- 修复存储桶配置环境变量覆盖问题
- 优化MinIO配置加载逻辑
- 改进Docker Compose部署稳定性

### v2.1.0 (2025-07-22)
**功能完善**：
- 完善多特征提取接口
- 优化健康度评估算法
- 增强异常检测准确性

### v2.0.0 (2025-07-17)
**初始发布版本**：
- 基础API接口实现
- 频率幅值分析功能
- 多特征提取功能
- 健康度评估功能
- Docker容器化部署
