# 振动监测系统 - API接口对接说明

**版本**: v2.2.0 Final | **Python**: 3.10.16 | **更新日期**: 2025-07-31

## 🚀 系统概述

本系统采用**Gunicorn高性能架构**，专为高频调用场景优化，支持每5秒10次并发调用。

### 核心优化特性
- **高性能处理**：Gunicorn多进程架构，支持并发处理
- **智能数据压缩**：可选压缩减少75%传输量
- **实时性能监控**：每个请求返回详细性能指标
- **自动降级机制**：确保服务高可用性

## 1. 接口概述

### 1.1 基本信息
- **服务地址**：http://your-server-ip:5000
- **协议**：HTTP/HTTPS
- **数据格式**：JSON
- **字符编码**：UTF-8
- **请求方式**：GET/POST
- **并发能力**：支持10+并发用户
- **响应时间**：< 5秒（优化后）

### 1.2 通用响应格式
```json
{
    "status": "success|error",
    "message": "响应消息",
    "result": {},
    "timestamp": "2024-01-01T12:00:00Z",
    "performance": {
        "total_time_ms": 1500.25,
        "load_time_ms": 200.15,
        "compute_time_ms": 1200.10,
        "data_points": 4000,
        "compression_enabled": true
    }
}
```

### 1.3 错误码说明
| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 200 | 请求成功 | - |
| 400 | 请求参数错误 | 检查请求参数格式和必填项 |
| 404 | 接口不存在 | 检查接口路径是否正确 |
| 500 | 服务器内部错误 | 联系技术支持 |
| 503 | 服务暂时不可用 | 稍后重试或检查服务状态 |

## 2. 接口详细说明

### 2.1 健康检查接口

**接口描述**：检查服务运行状态

**请求信息**：
- **URL**：`GET /health`
- **参数**：无

**响应示例**：
```json
{
    "status": "healthy",
    "service": "vibration-monitoring",
    "timestamp": "2025-07-22T10:56:10.787030",
    "version": "2.1.0",
    "model_loaded": true,
    "directories": {
        "logs": true,
        "csv_data": true
    },
    "memory_usage_percent": 96.6,
    "memory_available_gb": 0.53
}
```

**响应字段说明**：
| 字段名 | 类型 | 说明 |
|--------|------|------|
| status | string | 服务状态：healthy/unhealthy |
| service | string | 服务名称 |
| timestamp | string | 响应时间戳 |
| version | string | 服务版本号 |
| model_loaded | boolean | 模型是否已加载 |
| directories | object | 关键目录存在状态 |
| memory_usage_percent | number | 内存使用百分比 |
| memory_available_gb | number | 可用内存(GB) |

**调用示例**：
```bash
curl -X GET http://localhost:5000/health
```

### 2.2 API状态检查接口

**接口描述**：获取API服务状态和模型加载状态

**请求信息**：
- **URL**：`GET /api/status`
- **参数**：无

**响应示例**：
```json
{
    "status": "running",
    "model_loaded": true,
    "timestamp": "2025-07-22T10:56:19.444685",
    "version": "2.1.0"
}
```

**响应字段说明**：
| 字段名 | 类型 | 说明 |
|--------|------|------|
| status | string | API运行状态：running |
| model_loaded | boolean | 机器学习模型是否已加载 |
| timestamp | string | 响应时间戳 |
| version | string | API版本号 |

**调用示例**：
```bash
curl -X GET http://localhost:5000/api/status
```

### 2.3 设备配置查询接口

**接口描述**：获取所有可用设备及其轴承系数配置

**请求信息**：
- **URL**：`GET /api/devices`
- **Content-Type**：application/json

**响应示例**：
```json
{
    "status": "success",
    "timestamp": "2025-07-22T10:56:27.255076",
    "data": {
        "total_devices": 10,
        "devices": {
            "abc-b1": {
                "outer_coefficient": 3.096,
                "inner_coefficient": 4.94,
                "description": "外圈系数: 3.096, 内圈系数: 4.94"
            },
            "abc-b2": {
                "outer_coefficient": 3.096,
                "inner_coefficient": 4.94,
                "description": "外圈系数: 3.096, 内圈系数: 4.94"
            },
            "dev-a1": {
                "outer_coefficient": 5.766,
                "inner_coefficient": 8.234,
                "description": "外圈系数: 5.766, 内圈系数: 8.234"
            },
            "dev-a2": {
                "outer_coefficient": 5.766,
                "inner_coefficient": 8.234,
                "description": "外圈系数: 5.766, 内圈系数: 8.234"
            },
            "dji-d1": {
                "outer_coefficient": 3.037,
                "inner_coefficient": 4.963,
                "description": "外圈系数: 3.037, 内圈系数: 4.963"
            },
            "hsb-c1": {
                "outer_coefficient": 3.076,
                "inner_coefficient": 4.924,
                "description": "外圈系数: 3.076, 内圈系数: 4.924"
            }
        }
    }
}
```

**响应字段说明**：
| 字段名 | 类型 | 说明 |
|--------|------|------|
| status | string | 请求状态：success/error |
| timestamp | string | 响应时间戳 |
| data.total_devices | number | 可用设备总数 |
| data.devices | object | 设备配置字典 |
| outer_coefficient | number | 轴承外圈特征频率系数 |
| inner_coefficient | number | 轴承内圈特征频率系数 |
| description | string | 设备描述信息 |

**可用设备列表**：
- `abc-b1`, `abc-b2` - ABC系列设备
- `dev-a1`, `dev-a2`, `dev-a3`, `dev-a4` - DEV-A系列设备
- `dji-d1` - DJI系列设备
- `hsb-c1`, `hsb-c2`, `hsb-c3` - HSB系列设备

**调用示例**：
```bash
curl -X GET http://localhost:5000/api/devices
```

### 2.4 频率幅值分析接口（高性能优化版）

**接口描述**：获取振动信号的频率和幅值数据，专为高频调用场景优化

**性能特性**：
- ⚡ **响应时间**：< 5秒（优化前30+秒）
- 📦 **数据压缩**：可选压缩减少75%传输量
- 🔄 **并发支持**：支持10+并发用户
- 📊 **性能监控**：实时返回性能指标

**请求信息**：
- **URL**：`POST /api/features/freq_am`
- **Content-Type**：application/json

**请求参数**：
```json
{
    "equipment_code": "CAAQ000000497",
    "minio_url": "http://**************:9000/test1/data/input.csv",
    "rpm": 1800,
    "save_to_minio": true,
    "output_bucket": "test_online111"
}
```

**参数说明**：
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| equipment_code | string | 是 | - | 设备唯一标识码（必须是预定义的设备编码） |
| minio_url | string | 是 | - | MinIO CSV文件的完整URL地址 |
| rpm | number | 是 | - | 设备转速（转/分钟），用于计算轴承特征频率 |
| save_to_minio | boolean | 否 | true | 是否保存结果到MinIO |
| output_bucket | string | 否 | 配置文件 | 输出存储桶名称，不指定时使用配置文件中的默认值 |

**返回数据说明**：
| 字段名 | 类型 | 说明 |
|--------|------|------|
| spectrum_data | object | 包含总体、水平、轴向、垂直四个方向的频谱数据 |
| total_waveform | array | 三轴合成的总波形数据 |
| time_seconds | array | 时间序列数据（秒），与波形数据一一对应 |
| bearing_analysis | object | 轴承特征频率分析结果 |

**设备编码验证**：
- 系统会在参数验证阶段检查 `equipment_code` 是否存在于预定义设备列表中
- 如果设备编码不存在，将立即返回错误，不进行后续处理
- 错误信息会包含所有可用的设备编码列表，便于快速修正

**设备编码错误示例**：
```json
{
    "status": "error",
    "message": "未知设备编码: dev-a111。可用设备: abc-b1, abc-b2, dev-a1, dev-a2, dev-a3, dev-a4, dji-d1, hsb-c1, hsb-c2, hsb-c3",
    "timestamp": "2025-07-22T11:34:25.395799"
}
```

**响应示例**：

#### 1. 保存到MinIO模式 (save_to_minio=true，默认)
```json
{
    "status": "success",
    "timestamp": "2025-07-31T19:36:23.358744",
    "result": {
        "equipment_code": "CAAQ000000497",
        "minio_output": {
            "minio_url": "http://**************:9000/test_online111/2025/07/31/19/CAAQ000000497/1753940887102.csv",
            "bucket_name": "test_online111",
            "object_path": "2025/07/31/19/CAAQ000000497/1753940887102.csv",
            "file_size_bytes": 548982,
            "upload_time": "2025-07-31T19:36:23.358744"
        }
    }
}
```

#### 2. 直接返回数据模式 (save_to_minio=false)
```json
{
    "status": "success",
    "timestamp": "2025-07-31T19:36:23.358744",
    "result": {
        "equipment_code": "CAAQ000000497",
        "sampling_rate": 12000,
        "rpm": 1800,
        "spectrum_data": {
            "total": {
                "frequency": [0, 1.5, 3.0, 4.5, ...],
                "amplitude": [0.1, 0.2, 0.15, 0.18, ...],
                "data_points": 4000
            },
            "horizontal": {
                "frequency": [0, 1.5, 3.0, 4.5, ...],
                "amplitude": [0.05, 0.1, 0.08, 0.09, ...],
                "data_points": 4000
            },
            "axial": {
                "frequency": [0, 1.5, 3.0, 4.5, ...],
                "amplitude": [0.03, 0.08, 0.06, 0.07, ...],
                "data_points": 4000
            },
            "vertical": {
                "frequency": [0, 1.5, 3.0, 4.5, ...],
                "amplitude": [0.02, 0.06, 0.04, 0.05, ...],
                "data_points": 4000
            }
        },
        "total_waveform": [0.1, 0.2, 0.15, 0.18, ...],
        "time_seconds": [0.0, 0.000083, 0.000167, 0.000250, ...],
        "bearing_analysis": {
            "rotation_frequency_hz": 30.0,
            "recommended_outer_fcf": 105.0,
            "recommended_inner_fcf": 162.0,
            "device_coefficients": [5.766, 8.234]
        }
    }
}
```

**响应字段说明**：

#### 保存到MinIO模式响应字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| status | string | 请求状态：success/error |
| timestamp | string | 响应时间戳 |
| result.equipment_code | string | 设备编码 |
| result.minio_output.minio_url | string | MinIO文件访问URL |
| result.minio_output.bucket_name | string | 存储桶名称 |
| result.minio_output.object_path | string | 文件在存储桶中的路径 |
| result.minio_output.file_size_bytes | number | 文件大小（字节） |
| result.minio_output.upload_time | string | 上传时间戳 |

#### 直接返回数据模式响应字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| result.sampling_rate | number | 采样频率 |
| result.rpm | number | 设备转速 |
| result.spectrum_data.total | object | 总体频谱数据（频率+幅值数组） |
| result.spectrum_data.horizontal | object | 水平方向频谱数据 |
| result.spectrum_data.axial | object | 轴向频谱数据 |
| result.spectrum_data.vertical | object | 垂直方向频谱数据 |
| result.total_waveform | array | 总体波形数据 |
| result.time_seconds | array | 时间序列数据 |
| result.bearing_analysis | object | 轴承特征频率分析结果 |

**调用示例**：
```bash
# 基础调用（保存到MinIO，默认模式）
curl -X POST http://localhost:5000/api/features/freq_am \
  -H "Content-Type: application/json" \
  -d '{
    "equipment_code": "CAAQ000000497",
    "minio_url": "http://**************:9000/test1/data/input.csv",
    "rpm": 1800
  }'

# 指定输出存储桶
curl -X POST http://localhost:5000/api/features/freq_am \
  -H "Content-Type: application/json" \
  -d '{
    "equipment_code": "CAAQ000000497",
    "minio_url": "http://**************:9000/test1/data/input.csv",
    "rpm": 1800,
    "output_bucket": "custom-output-bucket"
  }'

# 直接返回数据（不保存到MinIO）
curl -X POST http://localhost:5000/api/features/freq_am \
  -H "Content-Type: application/json" \
  -d '{
    "equipment_code": "CAAQ000000497",
    "minio_url": "http://**************:9000/test1/data/input.csv",
    "rpm": 1800,
    "save_to_minio": false
  }'
```

### 2.5 多特征分析接口

**接口描述**：获取振动信号的多种特征数据和健康评估结果

**请求信息**：
- **URL**：`POST /api/features/multi_features`
- **Content-Type**：application/json

**请求参数**：
```json
{
    "equipment_code": "CAAQ000000497",
    "minio_url": "http://**************:9000/test1/data/input.csv",
    "health_degree_flag": 1,
    "predict_flag": 1,
    "early_warning_status": 0,
    "alarm_status": 0,
    "mtce_period": 365
}
```

**参数说明**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| equipment_code | string | 是 | 设备唯一标识码（必须是预定义的设备编码） |
| minio_url | string | 是 | MinIO CSV文件的完整URL地址 |
| health_degree_flag | int | 是 | 是否计算健康度 (0=否, 1=是) |
| predict_flag | int | 是 | 是否进行预测 (0=否, 1=是) |
| early_warning_status | int | 是 | 预警状态 (≥0的整数) |
| alarm_status | int | 是 | 报警状态 (≥0的整数) |
| mtce_period | int | 否 | 维护周期（天），默认365天 |

**设备编码验证**：
- 与频率幅值分析接口相同，系统会验证设备编码的有效性
- 无效的设备编码将立即返回错误，包含可用设备列表

**响应示例**：
```json
{
    "status": "success",
    "timestamp": "2025-07-22T10:56:37.308479",
    "result": {
        "equipment_code": "dev-a1",
        "rms_values": {
            "horizontal": 0.187,
            "axial": 0.249,
            "vertical": 0.111,
            "total": 0.374
        },
        "peak_to_peak": {
            "horizontal": 1.616,
            "axial": 1.821,
            "vertical": 1.146,
            "total": 2.986
        },
        "mean_values": {
            "horizontal": -0.018,
            "axial": -0.023,
            "vertical": -0.034,
            "total": -0.076
        },
        "kurtosis_values": {
            "horizontal": 4.251,
            "axial": 3.703,
            "vertical": 4.156,
            "total": 3.884
        },
        "predict_result": 1.0,
        "health_degree": 80.73,
        "mtce_advice": 182,
        "metadata": {
            "sampling_rate": 12000,
            "prediction_enabled": true,
            "health_assessment_enabled": true
        }
    }
}
```

**响应字段说明**：
| 字段名 | 类型 | 说明 |
|--------|------|------|
| status | string | 请求状态：success/error |
| timestamp | string | 响应时间戳 |
| result | object | 分析结果数据 |
| result.equipment_code | string | 设备编码 |
| result.rms_values | object | RMS有效值（水平、轴向、垂直、总计） |
| result.peak_to_peak | object | 峰峰值（水平、轴向、垂直、总计） |
| result.mean_values | object | 均值（水平、轴向、垂直、总计） |
| result.kurtosis_values | object | 峭度值（水平、轴向、垂直、总计） |
| result.predict_result | number | SVM预测结果（当predict_flag=1时，1.0=异常，0.0=正常） |
| result.health_degree | number | 健康度评分（当health_degree_flag=1时） |
| result.mtce_advice | number | 建议维护时间（天），当health_degree_flag=1时返回 |
| result.metadata | object | 元数据信息 |
| result.metadata.sampling_rate | number | 采样频率 |
| result.metadata.prediction_enabled | boolean | 是否启用预测 |
| result.metadata.health_assessment_enabled | boolean | 是否启用健康度评估 |

**调用示例**：
```bash
# 基础调用（包含维护建议）
curl -X POST http://localhost:5000/api/features/multi_features \
  -H "Content-Type: application/json" \
  -d '{
    "equipment_code": "CAAQ000000497",
    "minio_url": "http://**************:9000/test1/data/input.csv",
    "health_degree_flag": 1,
    "predict_flag": 1,
    "early_warning_status": 0,
    "alarm_status": 0,
    "mtce_period": 365
  }'

# 自定义维护周期
curl -X POST http://localhost:5000/api/features/multi_features \
  -H "Content-Type: application/json" \
  -d '{
    "equipment_code": "CAAQ000000497",
    "minio_url": "http://**************:9000/test1/data/input.csv",
    "health_degree_flag": 1,
    "predict_flag": 1,
    "early_warning_status": 0,
    "alarm_status": 0,
    "mtce_period": 180
  }'
```

## 3. 数据格式要求

### 3.1 CSV文件格式
CSV文件必须包含以下三列：
- `horizontal_vibration`：水平方向振动数据
- `axial_vibration`：轴向振动数据  
- `vertical_vibration`：垂直方向振动数据

**示例CSV文件**：
```csv
horizontal_vibration,axial_vibration,vertical_vibration
0.123,0.098,0.156
0.134,0.087,0.167
0.145,0.076,0.178
...
```

### 3.2 数据要求
- **数据类型**：数值型（浮点数）
- **数据范围**：建议在-10到10之间
- **采样频率**：建议12000Hz
- **数据长度**：100-12000个采样点
- **文件大小**：建议不超过20MB

## 4. 集成示例

### 4.1 Python集成示例
```python
import requests
import json

# 服务器地址
BASE_URL = "http://localhost:5000"

def check_service_status():
    """检查服务状态"""
    response = requests.get(f"{BASE_URL}/health")
    return response.json()

def get_frequency_amplitude(equipment_code, minio_url, rpm, save_to_minio=True, output_bucket=None):
    """获取频率幅值数据"""
    url = f"{BASE_URL}/api/features/freq_am"
    data = {
        "equipment_code": equipment_code,
        "minio_url": minio_url,
        "rpm": rpm,
        "save_to_minio": save_to_minio
    }
    if output_bucket:
        data["output_bucket"] = output_bucket
    response = requests.post(url, json=data, timeout=60)  # 设置超时
    return response.json()

def get_multi_features(equipment_code, minio_url, health_degree_flag=1, predict_flag=1,
                      early_warning_status=0, alarm_status=0, mtce_period=365):
    """获取多特征分析结果（包含维护建议）"""
    url = f"{BASE_URL}/api/features/multi_features"
    data = {
        "equipment_code": equipment_code,
        "minio_url": minio_url,
        "health_degree_flag": health_degree_flag,
        "predict_flag": predict_flag,
        "early_warning_status": early_warning_status,
        "alarm_status": alarm_status,
        "mtce_period": mtce_period
    }
    response = requests.post(url, json=data)
    return response.json()

# 使用示例
if __name__ == "__main__":
    # 检查服务状态
    status = check_service_status()
    print("服务状态:", status)

    # 获取频率幅值数据（保存到MinIO）
    freq_data = get_frequency_amplitude(
        "CAAQ000000497",
        "http://**************:9000/test1/data/input.csv",
        1800
    )
    print("频率幅值数据:", freq_data)

    # 获取多特征分析（包含维护建议）
    features = get_multi_features(
        "CAAQ000000497",
        "http://**************:9000/test1/data/input.csv",
        health_degree_flag=1,
        predict_flag=1,
        mtce_period=365
    )
    print("多特征分析:", features)
```

### 4.2 JavaScript集成示例
```javascript
// 服务器地址
const BASE_URL = "http://localhost:5000";

// 检查服务状态
async function checkServiceStatus() {
    try {
        const response = await fetch(`${BASE_URL}/health`);
        const data = await response.json();
        return data;
    } catch (error) {
        console.error("服务状态检查失败:", error);
        return null;
    }
}

// 获取频率幅值数据
async function getFrequencyAmplitude(equipmentCode, minioUrl, rpm, saveToMinio = true, outputBucket = null) {
    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 60000); // 60秒超时

        const requestBody = {
            equipment_code: equipmentCode,
            minio_url: minioUrl,
            rpm: rpm,
            save_to_minio: saveToMinio
        };

        if (outputBucket) {
            requestBody.output_bucket = outputBucket;
        }

        const response = await fetch(`${BASE_URL}/api/features/freq_am`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
            signal: controller.signal
        });

        clearTimeout(timeoutId);
        const data = await response.json();

        return data;
    } catch (error) {
        console.error("获取频率幅值数据失败:", error);
        return null;
    }
}

// 获取多特征分析（包含维护建议）
async function getMultiFeatures(equipmentCode, minioUrl, healthDegreeFlag = 1,
                               predictFlag = 1, earlyWarningStatus = 0, alarmStatus = 0, mtcePeriod = 365) {
    try {
        const response = await fetch(`${BASE_URL}/api/features/multi_features`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                equipment_code: equipmentCode,
                minio_url: minioUrl,
                health_degree_flag: healthDegreeFlag,
                predict_flag: predictFlag,
                early_warning_status: earlyWarningStatus,
                alarm_status: alarmStatus,
                mtce_period: mtcePeriod
            })
        });
        const data = await response.json();
        return data;
    } catch (error) {
        console.error("获取多特征分析失败:", error);
        return null;
    }
}

// 使用示例
async function main() {
    // 检查服务状态
    const status = await checkServiceStatus();
    console.log("服务状态:", status);

    // 获取频率幅值数据（保存到MinIO）
    const freqData = await getFrequencyAmplitude(
        "CAAQ000000497",
        "http://**************:9000/test1/data/input.csv",
        1800
    );
    console.log("频率幅值数据:", freqData);

    // 获取多特征分析（包含维护建议）
    const features = await getMultiFeatures(
        "CAAQ000000497",
        "http://**************:9000/test1/data/input.csv",
        1, 1, 0, 0, 365
    );
    console.log("多特征分析:", features);
}

main();
```

## 5. 常见问题

### 5.1 接口调用失败
**问题**：接口返回404错误
**解决方案**：
1. 检查URL路径是否正确
2. 确认服务是否正常运行
3. 检查端口号是否正确

### 5.2 数据格式错误
**问题**：接口返回400错误
**解决方案**：
1. 检查CSV文件格式是否正确（必须包含time_seconds, sample_index, horizontal_vibration, axial_vibration, vertical_vibration列）
2. 确认必填参数是否提供（equipment_code, minio_url等）
3. 检查数据类型是否匹配
4. 验证设备编码是否在预定义列表中

### 5.3 MinIO连接问题
**问题**：无法访问MinIO文件或上传失败
**解决方案**：
1. 检查MinIO URL格式是否正确
2. 确认MinIO服务器是否可访问
3. 验证存储桶是否存在
4. 检查MinIO访问密钥配置
5. 确认网络连接是否正常

### 5.4 模型未加载
**问题**：接口返回模型未加载错误
**解决方案**：
1. 检查模型文件是否存在
2. 重启服务重新加载模型
3. 检查模型文件权限

### 5.5 维护建议异常
**问题**：mtce_advice返回null或异常值
**解决方案**：
1. 确认health_degree_flag=1
2. 检查健康度计算是否正常
3. 验证mtce_period参数范围（1-3650天）
4. 检查设备数据质量

### 5.6 性能优化建议
1. **批量处理**：对于大量数据，建议分批处理
2. **缓存结果**：对于相同数据，可以缓存分析结果
3. **异步调用**：使用异步方式调用API，提高并发性能
4. **启用压缩**：在请求中设置 `compress_output: true` 减少传输量
5. **监控性能**：使用 `/api/performance/stats` 接口监控系统性能

## 6. 新增接口 (v2.0.0)

### 6.1 性能统计接口
**接口地址**：`GET /api/performance/stats`
**功能说明**：获取系统性能统计信息

**响应示例**：
```json
{
    "status": "success",
    "data": {
        "total_requests": 1250,
        "avg_response_time_ms": 1456.78,
        "requests_per_minute": 12.5,
        "error_rate": 0.02,
        "memory_usage_mb": 245.6,
        "cpu_usage_percent": 15.3,
        "uptime_seconds": 86400,
        "last_updated": "2025-07-17T10:30:00Z"
    }
}
```

### 6.2 根路径信息接口
**接口地址**：`GET /`
**功能说明**：获取API服务基本信息

**响应示例**：
```json
{
    "service": "振动监测系统 API",
    "version": "2.1.0",
    "description": "高性能振动信号分析API服务",
    "features": [
        "频率幅值分析",
        "多特征提取",
        "健康度评估",
        "异常检测预测"
    ],
    "performance": {
        "server": "Gunicorn多进程",
        "optimization": "高频调用优化",
        "compression": "智能数据压缩",
        "response_time": "< 5秒"
    },
    "endpoints": {
        "health": "/health",
        "api_status": "/api/status",
        "freq_am": "/api/features/freq_am",
        "multi_features": "/api/features/multi_features",
        "performance_stats": "/api/performance/stats"
    }
}
```

## 版本更新记录

### v2.2.0 (2025-07-31)
**新增功能**：
- ✅ **维护建议功能**: multi_features接口新增维护周期和维护建议计算
  - 新增`mtce_period`入参：维护周期（天），可选参数，默认365天
  - 新增`mtce_advice`出参：建议维护时间（天），基于健康度智能计算
  - 使用Sigmoid函数模型，科学预测设备维护时间

**技术改进**：
- ✅ **配置覆盖机制优化**: 修复了存储桶配置的环境变量覆盖问题
- ✅ **Docker部署优化**: 简化了部署配置，移除不必要的SECRET_KEY
- ✅ **资源检测增强**: 改进了容器内存检测机制，支持智能资源分配

**Bug修复**：
- 🔧 修复了`get_output_bucket()`方法不支持环境变量覆盖的问题
- 🔧 修复了Docker Compose环境变量映射失效的问题
- 🔧 优化了MinIO配置加载逻辑，确保环境变量优先级正确

### v2.1.0 (2025-07-22)
**功能完善**：
- ✅ 完善了多特征提取接口
- ✅ 优化了健康度评估算法
- ✅ 增强了异常检测准确性

### v2.0.0 (2025-07-17)
**初始版本**：
- ✅ 基础API接口实现
- ✅ 频率幅值分析功能
- ✅ 多特征提取功能
- ✅ 健康度评估功能
4. **数据预处理**：在客户端进行数据预处理，减少传输量
