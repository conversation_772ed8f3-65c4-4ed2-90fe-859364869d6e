#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
振动监测系统统一启动脚本 - 终极合并版本
整合了所有启动脚本的功能，支持智能服务器选择和性能优化

=== 快速启动 ===
python run.py --production          # 生产环境，自动选择最佳服务器
python run.py --development         # 开发环境，Flask服务器
python run.py --monitoring          # 监控环境，详细日志
python run.py --silent              # 静默模式，最少日志

=== 高性能模式 ===
python run.py --production --performance    # 启用所有性能优化
python run.py --production --server auto    # 自动选择最佳服务器

=== 服务器选择 ===
- auto: 自动选择（Windows用Waitress，Linux/macOS用Gunicorn）
- flask: Flask开发服务器
- waitress: Waitress服务器（推荐Windows）
- gunicorn: Gunicorn服务器（推荐Linux/macOS）

=== 日志级别说明 ===
- silent: 几乎无日志输出，只记录错误
- minimal: 只记录基本API信息，适用于高频调用的生产环境
- standard: 记录关键处理步骤，适用于正常监控和运维
- detailed: 记录所有详细信息，适用于开发调试和故障排查

=== 运行模式说明 ===
- production: 生产模式，关闭调试，启用文件日志，自动选择高性能服务器
- development: 开发模式，启用调试，详细日志输出，使用Flask服务器
- monitoring: 监控模式，关闭调试，详细日志，使用高性能服务器

=== 性能优化 ===
--performance 参数启用：
- 数据缓存和并行处理
- 优化的CSV读取和向量化计算

=== 日志管理命令 ===
查看日志统计：python -m tools.log_manager --analyze
压缩旧日志：  python -m tools.log_manager --compress --days 7
清理过期日志：python -m tools.log_manager --clean --expire 30
"""

import os
import sys
import argparse
import logging
import platform
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from vibmon_app import create_app


def setup_performance_environment():
    """设置性能优化环境变量"""
    performance_env = {
        'FLASK_ENV': 'production',
        'PYTHONUNBUFFERED': '1',
        'ENABLE_DATA_COMPRESSION': 'true',
        'COMPRESSED_DATA_POINTS': '4000',
        'CSV_ENGINE': 'c',
        'CSV_CHUNK_SIZE': '10000'
    }

    for key, value in performance_env.items():
        os.environ[key] = value

    print("⚡ 性能优化环境已启用")


def detect_best_server():
    """检测最佳服务器类型"""
    is_windows = platform.system().lower() == 'windows'

    if is_windows:
        # Windows优先使用Waitress
        try:
            import waitress
            return 'waitress'
        except ImportError:
            pass
    else:
        # Linux/macOS优先使用Gunicorn
        try:
            import gunicorn
            return 'gunicorn'
        except ImportError:
            pass

    # 降级到Flask
    return 'flask'


def create_directories():
    """创建必要的目录"""
    dirs = ['logs', 'csv_data', 'uploads']
    for dir_name in dirs:
        Path(dir_name).mkdir(exist_ok=True)


def start_production_server(host, port, mode, server_type='auto', workers=None, threads=None):
    """
    启动生产服务器（智能选择最佳服务器）

    Args:
        host (str): 监听地址
        port (int): 监听端口
        mode (str): 运行模式
        server_type (str): 服务器类型
        workers (int): 工作进程数（Gunicorn）
        threads (int): 线程数（Waitress）
    """
    # 自动检测最佳服务器
    if server_type == 'auto':
        server_type = detect_best_server()

    # 根据服务器类型启动
    if server_type == 'waitress':
        return start_waitress_server(host, port, mode, threads)
    elif server_type == 'gunicorn':
        return start_gunicorn_server(host, port, mode, workers)
    else:  # flask
        debug_mode = mode == 'development'
        return start_flask_server(host, port, debug_mode)


def start_waitress_server(host, port, mode, threads=None):
    """
    启动Waitress服务器（Windows推荐）

    Args:
        host (str): 监听地址
        port (int): 监听端口
        mode (str): 运行模式
        threads (int): 线程数
    """
    try:
        # 检查waitress是否安装
        import waitress
        try:
            version = waitress.__version__
        except AttributeError:
            # 某些版本的waitress没有__version__属性，使用现代方法获取版本
            try:
                from importlib.metadata import version as get_version
                version = get_version('waitress')
            except ImportError:
                # Python < 3.8 的降级方案
                try:
                    import pkg_resources
                    version = pkg_resources.get_distribution('waitress').version
                except Exception:
                    version = "unknown"
        print(f"✓ 使用Waitress {version} 启动高性能服务器（Windows优化）")
    except ImportError:
        print("❌ Waitress未安装，请运行: pip install waitress")
        print("🔄 降级使用Flask开发服务器...")
        return start_flask_server(host, port, mode == 'development')

    # 设置线程数
    if threads is None:
        if mode == 'production':
            threads = 16
        elif mode == 'monitoring':
            threads = 8
        else:
            threads = 4

    print(f"🚀 启动Waitress服务器")
    print(f"⚙️  线程数: {threads}")
    print("💡 使用 Ctrl+C 停止服务")

    try:
        # 导入应用
        app = create_app()

        # 启动Waitress服务器
        waitress.serve(
            app,
            host=host,
            port=port,
            threads=threads,
            connection_limit=1000,
            cleanup_interval=30,
            channel_timeout=120
        )
    except Exception as e:
        print(f"❌ Waitress启动失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n👋 用户中断，Waitress服务已停止")

    return True


def start_gunicorn_server(host, port, mode, workers=None):
    """
    启动Gunicorn服务器（Linux/macOS推荐）

    Args:
        host (str): 监听地址
        port (int): 监听端口
        mode (str): 运行模式
        workers (int): 工作进程数
    """
    import subprocess
    import sys

    try:
        # 检查gunicorn是否安装
        import gunicorn
        print(f"✓ 使用Gunicorn {gunicorn.__version__} 启动高性能服务器")
    except ImportError:
        print("❌ Gunicorn未安装，请运行: pip install gunicorn")
        print("🔄 降级使用Flask开发服务器...")
        return start_flask_server(host, port, mode == 'development')

    # 设置工作进程数
    if workers is None:
        if mode == 'production':
            workers = 8
        elif mode == 'monitoring':
            workers = 4
        else:
            workers = 2

    print(f"🚀 启动Gunicorn服务器")
    print(f"⚙️  工作进程: {workers}")
    print("💡 使用 Ctrl+C 停止服务")

    # 构建Gunicorn命令
    cmd = [
        sys.executable, '-m', 'gunicorn',
        '--config', 'gunicorn_config.py',
        '--bind', f'{host}:{port}',
        '--workers', str(workers),
        'run:app'
    ]

    try:
        # 启动Gunicorn
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Gunicorn启动失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n👋 用户中断，Gunicorn服务已停止")

    return True


def start_flask_server(host, port, debug_mode):
    """启动Flask开发服务器"""
    try:
        app = create_app()

        print("🔧 使用Flask开发服务器")
        if not debug_mode:
            print("⚠️  警告: 生产环境建议使用高性能服务器")

        app.run(
            debug=debug_mode,
            host=host,
            port=port,
            threaded=True,
            use_reloader=False
        )
    except KeyboardInterrupt:
        print("\n👋 用户中断，Flask服务已停止")

    return True


def set_environment_variables(mode, log_level):
    """
    根据运行模式设置环境变量

    Args:
        mode (str): 运行模式 (production/development/monitoring)
        log_level (str): 日志级别 (minimal/standard/detailed)
    """
    # 设置日志级别环境变量
    os.environ['VIBMON_LOG_LEVEL'] = log_level.upper()

    # 根据模式设置Flask环境变量
    if mode == 'production':
        os.environ['FLASK_DEBUG'] = 'False'
    elif mode == 'development':
        os.environ['FLASK_DEBUG'] = 'True'
    elif mode == 'monitoring':
        os.environ['FLASK_DEBUG'] = 'False'

    # 设置默认网络配置（如果未设置）
    if not os.environ.get('FLASK_HOST'):
        os.environ['FLASK_HOST'] = '0.0.0.0'

    if not os.environ.get('FLASK_PORT'):
        os.environ['FLASK_PORT'] = '5000'


def print_log_level_info(log_level, verbose=False):
    """
    打印日志级别信息

    Args:
        log_level (str): 日志级别
        verbose (bool): 是否显示详细信息
    """
    if not verbose:
        return

    # 简化的日志级别信息
    info = {
        'minimal': '最小日志 - 高性能模式',
        'standard': '标准日志 - 平衡模式',
        'detailed': '详细日志 - 调试模式'
    }

    description = info.get(log_level.lower(), '未知日志级别')
    print(f"📋 日志级别: {log_level.upper()} ({description})")


def print_optimization_tips():
    """打印日志优化建议"""
    print("\n💡 高频调用优化建议:")
    print("   1. 生产环境强烈建议使用 --log-level minimal")
    print("   2. 启用日志轮转和压缩: 配置已优化为2MB轮转，保留50个备份")
    print("   3. 定期清理: python -m tools.log_manager --compress --clean")
    print("   4. 监控磁盘使用: python -m tools.log_manager --analyze")
    print("   5. 考虑外部日志系统: ELK Stack、Fluentd等")
    print("   6. 异步日志写入: 已启用批量写入优化")


def print_usage_examples():
    """打印使用示例"""
    print("\n📖 使用示例:")
    print("   # 生产环境（推荐配置）")
    print("   python run.py --mode production --log-level minimal")
    print("")
    print("   # 开发环境")
    print("   python run.py --mode development --log-level detailed")
    print("")
    print("   # 监控环境")
    print("   python run.py --mode monitoring --log-level standard")
    print("")
    print("   # 自定义网络配置")
    print("   python run.py --host 127.0.0.1 --port 8080 --log-level minimal")


def show_startup_info(mode, log_level, server_type, host, port, performance_mode, quiet):
    """显示启动信息"""
    if quiet:
        if log_level == 'silent':
            print(f"🤫 {host}:{port}")
        else:
            print(f"🚀 {host}:{port}")
        return

    mode_icons = {
        'production': '🚀',
        'development': '🔧',
        'monitoring': '📊'
    }

    print(f"\n{mode_icons.get(mode, '⚙️')} 振动监测API服务 - 统一版")
    print(f"🌐 地址: http://{host}:{port}")
    print(f"📊 模式: {mode} | 日志: {log_level}")

    # 显示服务器类型
    server_names = {
        'waitress': 'Waitress (Windows高性能服务器)',
        'gunicorn': 'Gunicorn (Linux/macOS高性能服务器)',
        'flask': 'Flask开发服务器'
    }
    print(f"🔧 服务器: {server_names.get(server_type, server_type)}")

    if performance_mode:
        print("⚡ 高性能模式已启用")

    if log_level == 'silent':
        print("🤫 静默模式 - 几乎无日志")
    elif log_level == 'minimal':
        print("⚡ 最少日志输出")

    print("💡 使用 Ctrl+C 停止服务")
    print("=" * 50)


def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(
        description='振动监测系统统一启动脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
快速启动示例：
  python run.py --production          # 生产环境，自动选择最佳服务器
  python run.py --development         # 开发环境，Flask服务器
  python run.py --monitoring          # 监控环境，详细日志
  python run.py --silent              # 静默模式，最少日志
  python run.py --production --performance    # 高性能模式

服务器自动选择：
  Windows系统 → Waitress高性能服务器
  Linux/macOS → Gunicorn高性能服务器
  降级选项 → Flask开发服务器
        """
    )

    # 预设模式（互斥）
    mode_group = parser.add_mutually_exclusive_group()
    mode_group.add_argument('--production', action='store_true',
                           help='生产模式：MINIMAL日志，自动选择高性能服务器')
    mode_group.add_argument('--development', action='store_true',
                           help='开发模式：STANDARD日志，Flask服务器')
    mode_group.add_argument('--monitoring', action='store_true',
                           help='监控模式：DETAILED日志，高性能服务器')
    mode_group.add_argument('--silent', action='store_true',
                           help='静默模式：几乎无日志输出')

    # 自定义配置
    parser.add_argument('--mode', choices=['production', 'development', 'monitoring'],
                       help='自定义运行模式')
    parser.add_argument('--log-level', choices=['silent', 'minimal', 'standard', 'detailed'],
                       help='自定义日志级别')
    parser.add_argument('--server', choices=['auto', 'flask', 'waitress', 'gunicorn'],
                       default='auto', help='服务器类型（auto=自动选择）')


    # 性能优化
    parser.add_argument('--performance', action='store_true',
                       help='启用性能优化模式')

    # 网络配置
    parser.add_argument('--host', default='0.0.0.0', help='监听地址')
    parser.add_argument('--port', type=int, default=5000, help='监听端口')
    parser.add_argument('--workers', type=int, help='工作进程数（Gunicorn）')
    parser.add_argument('--threads', type=int, help='线程数（Waitress）')

    # 其他选项
    parser.add_argument('--quiet', '-q', action='store_true', help='静默输出')
    parser.add_argument('--info', action='store_true', help='显示配置信息')

    args = parser.parse_args()

    # 显示信息后退出
    if args.info:
        print("=" * 60)
        print("📊 振动监测系统配置信息")
        print("=" * 60)
        print_optimization_tips()
        print_usage_examples()
        return

    # 确定运行模式和日志级别
    if args.production:
        mode, log_level = 'production', 'minimal'
    elif args.development:
        mode, log_level = 'development', 'standard'
    elif args.monitoring:
        mode, log_level = 'monitoring', 'detailed'
    elif args.silent:
        mode, log_level = 'production', 'silent'
    else:
        mode = args.mode or 'development'
        log_level = args.log_level or 'standard'

    # 确定服务器类型
    if args.server == 'auto':
        server_type = detect_best_server()
    else:
        server_type = args.server

    # 设置环境变量
    set_environment_variables(mode, log_level)

    # 性能优化模式
    if args.performance:
        setup_performance_environment()

    # 创建目录
    create_directories()

    # 显示启动信息
    show_startup_info(mode, log_level, server_type, args.host, args.port,
                     args.performance, args.quiet)

    # 创建并启动应用
    try:
        # 启动服务器
        debug_mode = mode == 'development'

        success = start_production_server(
            args.host,
            args.port,
            mode,
            server_type,
            args.workers,
            args.threads
        )

        if not success:
            sys.exit(1)

    except KeyboardInterrupt:
        if not args.quiet:
            print("\n👋 用户中断，服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")
        if mode == 'development':
            import traceback
            traceback.print_exc()
        sys.exit(1)


# 创建应用实例供Gunicorn使用（仅在非主模块时创建）
if __name__ != '__main__':
    app = create_app()

if __name__ == '__main__':
    main()
