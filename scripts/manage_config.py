#!/usr/bin/env python3
"""
配置管理脚本
用于管理设备配置文件，支持添加、删除、修改设备配置
"""

import argparse
import yaml
import sys
import requests
from pathlib import Path
from datetime import datetime


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path="config/devices.yaml", api_url="http://localhost:5000"):
        self.config_path = Path(config_path)
        self.api_url = api_url
    
    def load_config(self):
        """加载配置文件"""
        if not self.config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        
        with open(self.config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def save_config(self, config):
        """保存配置文件"""
        # 更新最后修改时间
        config['last_updated'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        with open(self.config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
    
    def list_devices(self):
        """列出所有设备"""
        config = self.load_config()
        devices = config.get('devices', {})
        
        print(f"📋 设备配置列表 (共 {len(devices)} 个设备):")
        print("-" * 80)
        
        for device_code, device_info in devices.items():
            print(f"🔧 {device_code}")
            print(f"   外圈系数: {device_info['outer_coefficient']}")
            print(f"   内圈系数: {device_info['inner_coefficient']}")
            print(f"   描述: {device_info['description']}")
            print(f"   型号: {device_info.get('model', '未知')}")
            print(f"   位置: {device_info.get('location', '未知')}")
            print()
    
    def add_device(self, device_code, outer_coeff, inner_coeff, description, 
                   model="未知", location="未知", equipment_type="未知"):
        """添加设备"""
        config = self.load_config()
        
        if device_code in config['devices']:
            print(f"⚠️ 设备 {device_code} 已存在，是否要覆盖？(y/N): ", end="")
            if input().lower() != 'y':
                print("❌ 操作已取消")
                return False
        
        config['devices'][device_code] = {
            'outer_coefficient': float(outer_coeff),
            'inner_coefficient': float(inner_coeff),
            'description': description,
            'model': model,
            'location': location,
            'equipment_type': equipment_type
        }
        
        self.save_config(config)
        print(f"✅ 设备 {device_code} 添加成功")
        return True
    
    def remove_device(self, device_code):
        """删除设备"""
        config = self.load_config()
        
        if device_code not in config['devices']:
            print(f"❌ 设备 {device_code} 不存在")
            return False
        
        print(f"⚠️ 确认删除设备 {device_code}？(y/N): ", end="")
        if input().lower() != 'y':
            print("❌ 操作已取消")
            return False
        
        del config['devices'][device_code]
        self.save_config(config)
        print(f"✅ 设备 {device_code} 删除成功")
        return True
    
    def update_device(self, device_code, **kwargs):
        """更新设备配置"""
        config = self.load_config()
        
        if device_code not in config['devices']:
            print(f"❌ 设备 {device_code} 不存在")
            return False
        
        device_info = config['devices'][device_code]
        
        # 更新指定字段
        for key, value in kwargs.items():
            if value is not None:
                if key in ['outer_coefficient', 'inner_coefficient']:
                    device_info[key] = float(value)
                else:
                    device_info[key] = value
        
        self.save_config(config)
        print(f"✅ 设备 {device_code} 更新成功")
        return True
    
    def reload_api_config(self):
        """通过API重新加载配置"""
        try:
            response = requests.post(f"{self.api_url}/api/config/reload", timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ API配置重载成功")
                print(f"📊 设备数量: {result['data']['device_count']}")
                return True
            else:
                print(f"❌ API配置重载失败: {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            print("⚠️ 无法连接到API服务，请确保服务正在运行")
            return False
        except Exception as e:
            print(f"❌ API配置重载失败: {str(e)}")
            return False
    
    def validate_config(self):
        """验证配置文件"""
        try:
            config = self.load_config()
            devices = config.get('devices', {})
            
            print(f"🔍 验证配置文件...")
            
            errors = []
            
            for device_code, device_info in devices.items():
                # 检查必要字段
                required_fields = ['outer_coefficient', 'inner_coefficient', 'description']
                for field in required_fields:
                    if field not in device_info:
                        errors.append(f"设备 {device_code} 缺少字段: {field}")
                
                # 检查系数类型和范围
                for coeff_field in ['outer_coefficient', 'inner_coefficient']:
                    if coeff_field in device_info:
                        value = device_info[coeff_field]
                        if not isinstance(value, (int, float)) or value <= 0:
                            errors.append(f"设备 {device_code} 的 {coeff_field} 必须是正数")
            
            if errors:
                print("❌ 配置验证失败:")
                for error in errors:
                    print(f"  - {error}")
                return False
            else:
                print(f"✅ 配置验证通过，共 {len(devices)} 个设备")
                return True
                
        except Exception as e:
            print(f"❌ 配置验证失败: {str(e)}")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="设备配置管理工具")
    parser.add_argument("--config", default="config/devices.yaml", help="配置文件路径")
    parser.add_argument("--api-url", default="http://localhost:5000", help="API服务地址")
    
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 列出设备
    subparsers.add_parser("list", help="列出所有设备")
    
    # 添加设备
    add_parser = subparsers.add_parser("add", help="添加设备")
    add_parser.add_argument("device_code", help="设备编码")
    add_parser.add_argument("outer_coeff", type=float, help="外圈系数")
    add_parser.add_argument("inner_coeff", type=float, help="内圈系数")
    add_parser.add_argument("description", help="设备描述")
    add_parser.add_argument("--model", default="未知", help="设备型号")
    add_parser.add_argument("--location", default="未知", help="设备位置")
    add_parser.add_argument("--type", default="未知", help="设备类型")
    
    # 删除设备
    remove_parser = subparsers.add_parser("remove", help="删除设备")
    remove_parser.add_argument("device_code", help="设备编码")
    
    # 更新设备
    update_parser = subparsers.add_parser("update", help="更新设备")
    update_parser.add_argument("device_code", help="设备编码")
    update_parser.add_argument("--outer-coeff", type=float, help="外圈系数")
    update_parser.add_argument("--inner-coeff", type=float, help="内圈系数")
    update_parser.add_argument("--description", help="设备描述")
    update_parser.add_argument("--model", help="设备型号")
    update_parser.add_argument("--location", help="设备位置")
    update_parser.add_argument("--type", help="设备类型")
    
    # 重新加载配置
    subparsers.add_parser("reload", help="重新加载API配置")
    
    # 验证配置
    subparsers.add_parser("validate", help="验证配置文件")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    manager = ConfigManager(args.config, args.api_url)
    
    try:
        if args.command == "list":
            manager.list_devices()
        
        elif args.command == "add":
            manager.add_device(
                args.device_code, args.outer_coeff, args.inner_coeff,
                args.description, args.model, args.location, args.type
            )
        
        elif args.command == "remove":
            manager.remove_device(args.device_code)
        
        elif args.command == "update":
            manager.update_device(
                args.device_code,
                outer_coefficient=args.outer_coeff,
                inner_coefficient=args.inner_coeff,
                description=args.description,
                model=args.model,
                location=args.location,
                equipment_type=args.type
            )
        
        elif args.command == "reload":
            manager.reload_api_config()
        
        elif args.command == "validate":
            manager.validate_config()
    
    except Exception as e:
        print(f"❌ 操作失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
