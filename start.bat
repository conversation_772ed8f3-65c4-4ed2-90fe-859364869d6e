@echo off
REM 振动监测系统快速启动脚本 (Windows)

echo 🚀 振动监测系统
echo ==================

REM 创建必要目录
if not exist logs mkdir logs
if not exist csv_data mkdir csv_data
if not exist uploads mkdir uploads

REM 检查并启动服务器
python -c "import waitress" 2>nul
if errorlevel 1 (
    echo 🔧 使用 Flask 启动...
    python run.py --mode production --log-level minimal --quiet
) else (
    echo 🚀 使用 Waitress 启动...
    waitress-serve --host=0.0.0.0 --port=5000 --threads=8 run:app
)

pause
