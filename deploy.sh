#!/bin/bash

# 振动监测系统一键部署脚本
# 使用方法: ./deploy.sh

set -e

echo "🚀 振动监测系统 Docker 一键部署脚本"
echo "=================================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查Docker环境
check_docker() {
    print_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    print_success "Docker环境检查通过"
}

# 设置配置文件
set_config() {
    COMPOSE_FILE="docker-compose.yml"
    MODE="生产版"

    if [ ! -f "$COMPOSE_FILE" ]; then
        print_error "配置文件 $COMPOSE_FILE 不存在"
        exit 1
    fi

    print_success "使用配置文件: $COMPOSE_FILE"
}

# 配置参数
configure_parameters() {
    print_info "配置部署参数..."
    
    # 镜像地址
    echo ""
    read -p "请输入Docker镜像地址: " IMAGE_URL
    if [ -z "$IMAGE_URL" ]; then
        print_error "镜像地址不能为空"
        exit 1
    fi
    

    
    # MinIO配置
    echo ""
    read -p "请输入MinIO服务器地址: " MINIO_ENDPOINT
    read -p "请输入MinIO访问密钥: " MINIO_ACCESS_KEY
    read -p "请输入MinIO密钥: " MINIO_SECRET_KEY

    if [ -z "$MINIO_ENDPOINT" ] || [ -z "$MINIO_ACCESS_KEY" ] || [ -z "$MINIO_SECRET_KEY" ]; then
        print_error "MinIO配置不能为空"
        exit 1
    fi
}

# 应用配置
apply_configuration() {
    print_info "应用配置..."
    
    # 替换配置
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s|your-registry/vibmon:latest|$IMAGE_URL|g" docker-compose.yml
        sed -i '' "s|http://your-minio-server:9000|$MINIO_ENDPOINT|g" docker-compose.yml
        sed -i '' "s|your-minio-access-key|$MINIO_ACCESS_KEY|g" docker-compose.yml
        sed -i '' "s|your-minio-secret-key|$MINIO_SECRET_KEY|g" docker-compose.yml
    else
        # Linux
        sed -i "s|your-registry/vibmon:latest|$IMAGE_URL|g" docker-compose.yml
        sed -i "s|http://your-minio-server:9000|$MINIO_ENDPOINT|g" docker-compose.yml
        sed -i "s|your-minio-access-key|$MINIO_ACCESS_KEY|g" docker-compose.yml
        sed -i "s|your-minio-secret-key|$MINIO_SECRET_KEY|g" docker-compose.yml
    fi
    
    print_success "配置已应用"
}

# 启动服务
start_services() {
    print_info "启动服务..."
    
    # 创建目录
    mkdir -p logs predictive_model
    
    # 启动服务
    docker-compose up -d
    
    print_success "服务启动完成"
}

# 验证部署
verify_deployment() {
    print_info "验证部署 (等待60秒)..."
    sleep 60
    
    if curl -f http://localhost:5000/api/status &>/dev/null; then
        print_success "部署验证通过"
        return 0
    else
        print_error "部署验证失败"
        docker-compose logs vibmon-app
        return 1
    fi
}

# 显示结果
show_result() {
    echo ""
    print_success "🎉 部署完成！"
    echo ""
    echo "📋 访问信息:"
    echo "  应用地址: http://localhost:5000"
    echo "  健康检查: http://localhost:5000/api/status"
    echo "  设备列表: http://localhost:5000/api/devices"
    
    echo ""
    echo "🔧 管理命令:"
    echo "  查看状态: docker-compose ps"
    echo "  查看日志: docker-compose logs -f"
    echo "  停止服务: docker-compose down"
}

# 主函数
main() {
    check_docker
    set_config
    configure_parameters
    apply_configuration
    start_services

    if verify_deployment; then
        show_result
    else
        print_error "部署失败，请检查日志"
        exit 1
    fi
}

# 执行
main "$@"
