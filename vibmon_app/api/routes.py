#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API路由模块
"""

# 标准库导入
import json
import os
import time
import psutil
from datetime import datetime

# 第三方库导入
import pandas as pd
from flask import Blueprint, request, jsonify, current_app, make_response

# 本地导入
from vibmon_app.api.validators import RequestValidator
from vibmon_app.config import Config
from vibmon_app.models.ml_models import model_manager
from vibmon_app.services.bearing_frequency_calculator import BearingFrequencyCalculator
from vibmon_app.services.feature_extraction import FeatureExtractor
from vibmon_app.services.health_assessment import HealthAssessor
from vibmon_app.services.optimized_signal_processing import OptimizedSignalProcessor
from vibmon_app.services.signal_processing import SignalProcessor
from vibmon_app.utils.config_loader import reload_device_config, get_device_config_loader
from vibmon_app.utils.csv_formatter import CSVFormatter
from vibmon_app.utils.exceptions import (
    VibrationAnalysisError, ModelNotLoadedError,
    InvalidDataError, PredictionError
)

from vibmon_app.utils.helpers import setup_numpy_warnings
from vibmon_app.utils.logging_utils import (
    log_api_call, log_request_params, log_processing_step, log_result_summary
)
from vibmon_app.utils.minio_client import MinIOClient
from vibmon_app.utils.maintenance_calculator import MaintenanceCalculator

# 创建蓝图
api_bp = Blueprint('api', __name__)

# 创建根路由蓝图
root_bp = Blueprint('root', __name__)


def create_json_response(data, status_code=200):
    """
    创建正确编码的JSON响应

    Args:
        data: 要序列化的数据
        status_code: HTTP状态码

    Returns:
        Flask Response对象
    """
    response = make_response(
        json.dumps(data, ensure_ascii=False, indent=2),
        status_code
    )
    response.headers['Content-Type'] = 'application/json; charset=utf-8'
    return response


# 设置NumPy警告
@api_bp.before_request
def before_request():
    setup_numpy_warnings()


@api_bp.route('/status', methods=['GET'])
@log_api_call
def get_status():
    """API状态检查端点"""
    # 日志记录已由装饰器处理，避免重复记录

    return jsonify({
        "status": "running",
        "model_loaded": model_manager.is_model_loaded(),
        "timestamp": datetime.now().isoformat(),
        "version": "2.2.0"
    })


@api_bp.route('/devices', methods=['GET'])
@log_api_call
def get_devices():
    """
    获取可用设备配置端点

    返回:
        JSON响应包含所有可用设备及其轴承系数配置
    """
    try:
        devices = BearingFrequencyCalculator.get_available_devices()

        # 格式化设备信息
        formatted_devices = {}
        for device_code, coefficients in devices.items():
            formatted_devices[device_code] = {
                "outer_coefficient": coefficients[0],
                "inner_coefficient": coefficients[1],
                "description": f"外圈系数: {coefficients[0]}, 内圈系数: {coefficients[1]}"
            }

        return jsonify({
            "status": "success",
            "timestamp": datetime.now().isoformat(),
            "data": {
                "total_devices": len(formatted_devices),
                "devices": formatted_devices
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取设备配置失败: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"获取设备配置失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }), 500


@api_bp.route('/config/reload', methods=['POST'])
@log_api_call
def reload_config():
    """
    重新加载设备配置端点

    用于在Docker部署后动态重新加载YAML配置文件
    """
    try:
        # 重新加载配置
        success = reload_device_config()

        if success:
            # 同时更新Flask配置
            from vibmon_app.config import Config
            Config.reload_device_config()

            # 获取配置信息
            loader = get_device_config_loader()
            config_info = loader.get_config_info()
            devices = loader.get_devices()

            current_app.logger.info(f"配置重新加载成功，加载了 {len(devices)} 个设备")

            return jsonify({
                "status": "success",
                "message": "配置重新加载成功",
                "timestamp": datetime.now().isoformat(),
                "data": {
                    "config_info": config_info,
                    "device_count": len(devices),
                    "devices": list(devices.keys())
                }
            })
        else:
            return jsonify({
                "status": "error",
                "message": "配置重新加载失败",
                "timestamp": datetime.now().isoformat()
            }), 500

    except Exception as e:
        current_app.logger.error(f"重新加载配置失败: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"重新加载配置失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }), 500


@api_bp.route('/config/info', methods=['GET'])
@log_api_call
def get_config_info():
    """
    获取配置文件信息端点
    """
    try:
        loader = get_device_config_loader()
        config_info = loader.get_config_info()

        return jsonify({
            "status": "success",
            "timestamp": datetime.now().isoformat(),
            "data": config_info
        })

    except Exception as e:
        current_app.logger.error(f"获取配置信息失败: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"获取配置信息失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }), 500


@api_bp.route('/features/freq_am', methods=['POST'])
@log_api_call
def get_freq_am():
    """
    计算振动信号的频率和幅值

    从MinIO读取振动数据，进行频谱分析和轴承特征频率计算。
    支持将结果保存到MinIO或直接返回数据。

    针对高频调用场景优化：
    1. 优化的信号处理算法
    2. 减少日志开销
    3. 向量化操作提升计算效率
    4. MinIO集成支持

    请求参数:
        equipment_code (str): 设备编码，必须在devices.yaml中定义
        minio_url (str): MinIO CSV文件URL，格式：http://host:port/bucket/path/file.csv
        output_bucket (str, optional): 输出存储桶名称，默认使用配置文件中的output_bucket
        rpm (float): 设备转速 (转/分钟)，用于计算轴承特征频率
        save_to_minio (bool, optional): 是否保存结果到MinIO，默认true

    返回:
        JSON响应包含以下字段:
        - status: 请求状态 ("success" 或 "error")
        - timestamp: 响应时间戳
        - result: 结果数据
          - equipment_code: 设备编码
          - minio_output: MinIO保存信息 (当save_to_minio=true时)
          - spectrum_data: 频谱数据 (当save_to_minio=false时)
          - total_waveform: 总波形数据 (当save_to_minio=false时)
          - time_seconds: 时间序列 (当save_to_minio=false时)
          - bearing_analysis: 轴承特征频率分析结果 (当save_to_minio=false时)
    """
    start_time = time.time()

    try:
        # 获取JSON数据
        try:
            json_data = request.get_json()
            if json_data is None:
                return create_json_response({
                    "status": "error",
                    "message": "请求体不能为空或不是有效的JSON",
                    "timestamp": datetime.now().isoformat()
                }, 400)
        except Exception as e:
            return create_json_response({
                "status": "error",
                "message": f"JSON解析错误: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }, 400)

        # 验证请求参数
        validated_data = RequestValidator.validate_freq_am_request(json_data)

        # 获取参数
        save_to_minio = validated_data.get('save_to_minio', True)  # 从验证器获取，默认true
        minio_url = validated_data.get('minio_url')  # MinIO文件URL
        output_bucket = validated_data.get('output_bucket')  # 输出存储桶

        # 记录API调用信息（简化版，减少日志开销）
        if current_app.config.get('VIBMON_LOG_LEVEL', 'STANDARD') != 'MINIMAL':
            current_app.logger.info(
                f"[API_CALL] freq_am | "
                f"Equipment: {validated_data['equipment_code']} | "
                f"RPM: {validated_data['rpm']}"
            )

        # 从MinIO读取数据
        load_start = time.time()
        try:
            minio_client = MinIOClient()

            # 读取CSV文件
            df = minio_client.read_csv_from_minio(minio_url)

            # 提取振动数据
            max_samples = current_app.config['MAX_SAMPLES']
            data_h = df['horizontal_vibration'][:max_samples].tolist()
            data_a = df['axial_vibration'][:max_samples].tolist()
            data_v = df['vertical_vibration'][:max_samples].tolist()

            # 提取时间数据
            if 'time_seconds' in df.columns:
                time_seconds = df['time_seconds'][:max_samples].tolist()
            else:
                # 生成默认时间序列
                sampling_rate = 12000  # 默认采样率
                time_seconds = [i / sampling_rate for i in range(len(data_h))]

            # 计算采样率
            if len(time_seconds) > 1:
                sampling_rate = 1 / (time_seconds[1] - time_seconds[0])
            else:
                sampling_rate = 12000  # 默认采样率

            current_app.logger.info(f"从MinIO读取数据成功: {len(data_h)} 个样本")

        except Exception as e:
            current_app.logger.error(f"MinIO数据读取失败: {str(e)}")
            raise InvalidDataError(f"MinIO数据读取失败: {str(e)}")

        load_time = (time.time() - load_start) * 1000

        # 使用优化的信号处理器进行频谱计算
        compute_start = time.time()

        # 初始化优化的信号处理器
        processor = OptimizedSignalProcessor()

        # 计算包络谱（使用优化算法）
        freq_total, am_total = processor.envelope_spectrum_all_optimized(
            data_h, data_a, data_v, sampling_rate
        )

        freq_h, am_h = processor.envelope_spectrum_single_optimized(
            data_h, sampling_rate
        )
        freq_a, am_a = processor.envelope_spectrum_single_optimized(
            data_a, sampling_rate
        )
        freq_v, am_v = processor.envelope_spectrum_single_optimized(
            data_v, sampling_rate
        )

        # 计算轴承特征频率
        # 注意：设备编码已在RequestValidator.validate_freq_am_request中验证
        bearing_calc_start = time.time()
        bearing_frequencies = BearingFrequencyCalculator.calculate_characteristic_frequencies(
            validated_data['equipment_code'],
            validated_data['rpm']
        )

        # 总的波形图
        total_waveform = data_h + data_a + data_v

        bearing_calc_time = (time.time() - bearing_calc_start) * 1000
        compute_time = (time.time() - compute_start) * 1000
        total_time = (time.time() - start_time) * 1000

        # 构建频谱数据（用于MinIO保存）
        spectrum_data = {
            # 总体频谱数据
            "total": {
                "frequency": freq_total,
                "amplitude": am_total,
                "data_points": len(freq_total)
            },
            # 水平方向频谱数据
            "horizontal": {
                "frequency": freq_h,
                "amplitude": am_h,
                "data_points": len(freq_h)
            },
            # 轴向频谱数据
            "axial": {
                "frequency": freq_a,
                "amplitude": am_a,
                "data_points": len(freq_a)
            },
            # 垂直方向频谱数据
            "vertical": {
                "frequency": freq_v,
                "amplitude": am_v,
                "data_points": len(freq_v)
            }
        }

        # 轴承分析结果
        bearing_analysis = {
            "rotation_frequency_hz": bearing_frequencies.get('rotation_frequency', 0.0),
            "recommended_outer_fcf": bearing_frequencies.get('outer_fcf', 0.0),
            "recommended_inner_fcf": bearing_frequencies.get('inner_fcf', 0.0)
        }

        # 构建基础响应（不包含大数据）
        response = {
            "status": "success",
            "timestamp": datetime.now().isoformat(),
            "result": {
                # 基础信息
                "equipment_code": validated_data['equipment_code']
            }
        }

        # 如果不保存到MinIO，则返回完整数据（保持向后兼容）
        if not save_to_minio:
            response["result"].update({
                # 频谱数据
                "spectrum_data": spectrum_data,
                # 总波形数据
                "total_waveform": total_waveform,
                # 时间数据
                "time_seconds": time_seconds,
                # 轴承特征频率分析
                "bearing_analysis": bearing_analysis
            })

        # 如果需要保存到MinIO
        if save_to_minio:
            try:
                minio_client = MinIOClient()
                csv_formatter = CSVFormatter()

                # 格式化数据为CSV
                csv_df = csv_formatter.format_freq_am_result(
                    spectrum_data,
                    time_seconds,
                    total_waveform,
                    bearing_analysis
                )

                # 确定输出存储桶
                if not output_bucket:
                    output_bucket = minio_client.config.get_output_bucket()

                # 生成输出路径
                output_path = minio_client.generate_output_path(
                    validated_data['equipment_code'], output_bucket
                )

                # 保存到MinIO
                save_result = minio_client.save_csv_to_minio(
                    csv_df, output_bucket, output_path
                )

                # 添加MinIO信息到响应
                response["result"]["minio_output"] = {
                    "minio_url": save_result["minio_url"],
                    "bucket_name": save_result["bucket_name"],
                    "object_path": save_result["object_path"],
                    "file_size_bytes": save_result["file_size_bytes"],
                    "upload_time": save_result["upload_time"]
                }

                current_app.logger.info(f"结果已保存到MinIO: {save_result['minio_url']}")

            except Exception as e:
                current_app.logger.error(f"MinIO保存失败: {str(e)}")
                # 不影响主要功能，只记录错误
                response["result"]["minio_error"] = str(e)

        # 记录性能信息
        if current_app.config.get('VIBMON_LOG_LEVEL', 'STANDARD') != 'MINIMAL':
            current_app.logger.info(
                f"[PERFORMANCE] freq_am | "
                f"Total: {total_time:.1f}ms | Load: {load_time:.1f}ms | "
                f"Compute: {compute_time:.1f}ms | Points: {len(freq_total)}"
            )

        return create_json_response(response, 200)

    except InvalidDataError as e:
        current_app.logger.warning(f"频率幅值请求参数错误: {str(e)}")
        return create_json_response({
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }, 400)

    except Exception as e:
        current_app.logger.error(f"频率幅值计算失败: {str(e)}")
        return create_json_response({
            "status": "error",
            "message": f"服务器内部错误: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }, 500)


@api_bp.route('/features/multi_features', methods=['POST'])
@log_api_call
def get_multi_features():
    """
    提取多维度振动特征

    从MinIO读取振动数据，提取时域、频域、统计特征等多维度特征。
    支持健康度评估、故障预测和维护建议计算。

    请求参数:
        minio_url (str): MinIO CSV文件URL，格式：http://host:port/bucket/path/file.csv
        equipment_code (str): 设备编码，用于标识设备
        health_degree_flag (int): 是否计算健康度 (0=不计算, 1=计算)
        predict_flag (int): 是否进行SVM预测 (0=不预测, 1=预测)
        early_warning_status (int): 预警状态 (0=正常, 1=预警)
        alarm_status (int): 报警状态 (0=正常, 1=报警)
        mtce_period (int, optional): 维护周期（天），默认365，用于计算维护建议

    返回:
        JSON响应包含以下字段:
        - status: 请求状态 ("success" 或 "error")
        - timestamp: 响应时间戳
        - result: 结果数据
          - equipment_code: 设备编码
          - rms_values: RMS值 (包含horizontal, axial, vertical, total)
          - peak_to_peak: 峰峰值 (包含horizontal, axial, vertical, total)
          - mean_values: 均值 (包含horizontal, axial, vertical, total)
          - kurtosis_values: 峰度值 (包含horizontal, axial, vertical, total)
          - predict_result: SVM预测结果 (当predict_flag=1时)
          - health_degree: 健康度评估结果 (当health_degree_flag=1时)
          - mtce_advice: 建议维护时间（天） (当health_degree_flag=1时计算)
    """
    start_time = time.time()

    try:
        # 获取JSON数据，处理空请求体和无效JSON
        try:
            json_data = request.get_json()
            if json_data is None:
                return create_json_response({
                    "status": "error",
                    "message": "请求体不能为空或不是有效的JSON",
                    "timestamp": datetime.now().isoformat()
                }, 400)
        except Exception as e:
            return create_json_response({
                "status": "error",
                "message": f"JSON解析错误: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }, 400)

        # 验证请求参数
        validated_data = RequestValidator.validate_multi_features_request(json_data)

        # 获取MinIO读取参数
        minio_url = json_data.get('minio_url')

        # 记录验证后的参数
        log_request_params(validated_data, "多特征计算")

        # 从MinIO读取数据
        log_processing_step("从MinIO加载CSV数据", {"minio_url": minio_url})
        try:
            minio_client = MinIOClient()

            # 读取CSV文件
            df = minio_client.read_csv_from_minio(minio_url)

            # 提取振动数据
            max_samples = current_app.config['MAX_SAMPLES']
            data_h = df['horizontal_vibration'][:max_samples].tolist()
            data_a = df['axial_vibration'][:max_samples].tolist()
            data_v = df['vertical_vibration'][:max_samples].tolist()

            # 计算采样率
            if 'time_seconds' in df.columns and len(df) > 1:
                time_diff = df['time_seconds'].iloc[1] - df['time_seconds'].iloc[0]
                sampling_rate = 1 / time_diff if time_diff > 0 else 12000
            else:
                sampling_rate = 12000  # 默认采样率

            current_app.logger.info(f"从MinIO读取数据成功: {len(data_h)} 个样本")

        except Exception as e:
            current_app.logger.error(f"MinIO数据读取失败: {str(e)}")
            raise InvalidDataError(f"MinIO数据读取失败: {str(e)}")

        # 记录数据加载结果
        log_processing_step("数据加载完成", {
            "data_points": len(data_h),
            "sampling_rate": sampling_rate
        })

        # 提取特征
        log_processing_step("开始特征提取")
        feature_extractor = FeatureExtractor()
        features = feature_extractor.extract_signal_features(data_h, data_a, data_v)

        # 记录特征提取结果
        log_processing_step("特征提取完成", {
            "rms_total": features['rms']['total'],
            "total_amplitude": features['total_amplitude'],
            "total_kurtosis": features['total_kurtosis']
        })

        # 初始化结果变量
        prediction_result = current_app.config['DEFAULT_PREDICTION_RESULT']
        health_degree = None

        # 进行预测
        if validated_data['predict_flag'] == 1:
            log_processing_step("开始SVM预测", {"model_loaded": model_manager.is_model_loaded()})
            try:
                # 检查模型是否可用
                if not model_manager.is_model_loaded():
                    current_app.logger.warning("[WARNING] SVM模型未加载，使用默认预测结果")
                    prediction_result = current_app.config['DEFAULT_PREDICTION_RESULT']
                else:
                    # 计算包络谱获取幅值用于预测
                    signal_processor = SignalProcessor()
                    _, am = signal_processor.envelope_spectrum_all(data_h, data_a, data_v, sampling_rate)

                    # 验证幅值数据
                    if not am or len(am) == 0:
                        current_app.logger.error("包络谱幅值数据为空")
                        prediction_result = current_app.config['DEFAULT_PREDICTION_RESULT']
                    else:
                        prediction_result = model_manager.predict_with_svm(am)
                        log_processing_step("SVM预测完成", {
                            "prediction_result": prediction_result,
                            "amplitude_points": len(am)
                        })

            except (ModelNotLoadedError, PredictionError) as e:
                current_app.logger.error(f"SVM预测失败: {str(e)}", exc_info=True)
                prediction_result = current_app.config['DEFAULT_PREDICTION_RESULT']
            except Exception as e:
                current_app.logger.error(f"预测过程失败: {str(e)}", exc_info=True)
                prediction_result = current_app.config['DEFAULT_PREDICTION_RESULT']

        # 健康度评估
        if validated_data['health_degree_flag'] == 1:
            log_processing_step("开始健康度评估", {
                "early_warning_status": validated_data['early_warning_status'],
                "alarm_status": validated_data['alarm_status']
            })
            health_assessor = HealthAssessor()
            health_result = health_assessor.comprehensive_health_assessment(
                data_h, data_a, data_v,
                validated_data['early_warning_status'],
                validated_data['alarm_status']
            )
            health_degree = health_result['adjusted_health_degree']

            log_processing_step("健康度评估完成", {
                "base_health": health_result['base_health_degree'],
                "adjusted_health": health_degree,
                "health_status": health_result['health_status']['status']
            })

        # 计算维护建议
        mtce_advice = MaintenanceCalculator.calculate_maintenance_advice(
            health_degree=health_degree if validated_data['health_degree_flag'] == 1 else None,
            health_degree_flag=validated_data['health_degree_flag'],
            mtce_period=validated_data['mtce_period']
        )

        if mtce_advice is not None:
            log_processing_step("维护建议计算完成", {
                "health_degree": health_degree,
                "mtce_period": validated_data['mtce_period'],
                "mtce_advice": mtce_advice
            })

        # 准备响应数据
        response = {
            "status": "success",
            "timestamp": datetime.now().isoformat(),
            "result": {
                "equipment_code": validated_data['equipment_code'],
                "rms_values": {
                    "horizontal": features['rms']['h'],
                    "axial": features['rms']['a'],
                    "vertical": features['rms']['v'],
                    "total": features['rms']['total']
                },
                "peak_to_peak": {
                    "horizontal": features['p2p']['h'],
                    "axial": features['p2p']['a'],
                    "vertical": features['p2p']['v'],
                    "total": features['p2p']['total']
                },
                "mean_values": {
                    "horizontal": features['mean']['h'],
                    "axial": features['mean']['a'],
                    "vertical": features['mean']['v'],
                    "total": features['mean']['total']
                },
                "kurtosis_values": {
                    "horizontal": features['kurtosis']['h'],
                    "axial": features['kurtosis']['a'],
                    "vertical": features['kurtosis']['v'],
                    "total": features['total_kurtosis']
                },
                "predict_result": prediction_result,
                "health_degree": health_degree,
                "mtce_advice": mtce_advice
            }
        }



        # 记录结果摘要
        log_result_summary(response, "多特征计算")

        return jsonify(response)

    except (FileNotFoundError, ValueError) as e:
        current_app.logger.warning(f"多特征请求参数错误: {str(e)}", exc_info=True)
        return create_json_response({
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }, 400)

    except InvalidDataError as e:
        current_app.logger.warning(f"多特征数据验证错误: {str(e)}", exc_info=True)
        return create_json_response({
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }, 400)

    except Exception as e:
        current_app.logger.error(f"多特征计算未知错误: {str(e)}", exc_info=True)
        return create_json_response({
            "status": "error",
            "message": "内部服务器错误",
            "timestamp": datetime.now().isoformat()
        }, 500)


@api_bp.route('/performance/stats', methods=['GET'])
def get_performance_stats():
    """
    获取系统性能统计信息

    返回:
        JSON响应包含系统性能指标
    """
    try:
        # 获取系统信息
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        # 获取进程信息
        process = psutil.Process(os.getpid())
        process_memory = process.memory_info()

        # 获取Gunicorn worker信息（如果可用）
        workers_info = []
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                if 'gunicorn' in proc.info['name'] and 'worker' in ' '.join(proc.info['cmdline']):
                    worker_proc = psutil.Process(proc.info['pid'])
                    workers_info.append({
                        'pid': proc.info['pid'],
                        'cpu_percent': worker_proc.cpu_percent(),
                        'memory_mb': round(worker_proc.memory_info().rss / 1024 / 1024, 2),
                        'status': worker_proc.status()
                    })
        except (ImportError, AttributeError, OSError):
            # psutil不可用或权限不足，跳过进程信息收集
            pass

        # 构建响应
        stats = {
            "status": "success",
            "timestamp": datetime.now().isoformat(),
            "system": {
                "cpu_percent": cpu_percent,
                "memory": {
                    "total_gb": round(memory.total / 1024 / 1024 / 1024, 2),
                    "available_gb": round(memory.available / 1024 / 1024 / 1024, 2),
                    "used_percent": memory.percent
                },
                "disk": {
                    "total_gb": round(disk.total / 1024 / 1024 / 1024, 2),
                    "free_gb": round(disk.free / 1024 / 1024 / 1024, 2),
                    "used_percent": round((disk.used / disk.total) * 100, 2)
                }
            },
            "process": {
                "pid": os.getpid(),
                "memory_mb": round(process_memory.rss / 1024 / 1024, 2),
                "cpu_percent": process.cpu_percent(),
                "threads": process.num_threads(),
                "status": process.status()
            },
            "application": {
                "flask_env": os.environ.get('FLASK_ENV', 'unknown'),
                "log_level": os.environ.get('VIBMON_LOG_LEVEL', 'unknown'),
                "server_type": "gunicorn" if workers_info else "flask",
                "workers_count": len(workers_info)
            }
        }

        # 添加worker信息（如果有）
        if workers_info:
            stats["workers"] = workers_info
            stats["workers_summary"] = {
                "total_workers": len(workers_info),
                "total_memory_mb": round(sum(w['memory_mb'] for w in workers_info), 2),
                "avg_cpu_percent": round(sum(w['cpu_percent'] for w in workers_info) / len(workers_info), 2)
            }

        return create_json_response(stats, 200)

    except Exception as e:
        current_app.logger.error(f"获取性能统计失败: {str(e)}")
        return create_json_response({
            "status": "error",
            "message": f"获取性能统计失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }, 500)


@root_bp.route('/health', methods=['GET'])
def health_check():
    """
    健康检查接口
    用于Docker健康检查和负载均衡器检查

    返回:
        JSON响应包含服务健康状态
    """
    try:
        # 检查基本服务状态
        status = {
            "status": "healthy",
            "service": "vibration-monitoring",
            "timestamp": datetime.now().isoformat(),
            "version": "2.2.0"
        }

        # 检查模型加载状态
        try:
            model_loaded = model_manager.is_model_loaded()
            status["model_loaded"] = model_loaded
        except Exception as e:
            current_app.logger.warning(f"检查模型状态失败: {str(e)}")
            status["model_loaded"] = False

        # 检查关键目录
        directories = ['logs', 'csv_data']
        status["directories"] = {}
        for dir_name in directories:
            status["directories"][dir_name] = os.path.exists(dir_name)

        # 检查内存使用情况
        try:
            import psutil
            memory = psutil.virtual_memory()
            status["memory_usage_percent"] = memory.percent
            status["memory_available_gb"] = round(memory.available / 1024 / 1024 / 1024, 2)
        except ImportError:
            current_app.logger.debug("psutil未安装，跳过内存检查")
        except Exception as e:
            current_app.logger.warning(f"内存检查失败: {str(e)}")

        return create_json_response(status, 200)

    except Exception as e:
        # 健康检查失败
        error_status = {
            "status": "unhealthy",
            "service": "vibration-monitoring",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }
        return create_json_response(error_status, 503)


@root_bp.route('/', methods=['GET'])
def root():
    """
    根路径接口
    提供API基本信息
    """
    info = {
        "service": "振动监测系统 API",
        "version": "2.2.0",
        "description": "高性能振动信号分析API服务",
        "features": [
            "频率幅值分析",
            "多特征提取",
            "健康度评估",
            "异常检测预测",
            "智能维护建议"
        ],
        "performance": {
            "server": "Gunicorn多进程",
            "optimization": "高频调用优化",
            "compression": "智能数据压缩",
            "response_time": "< 5秒"
        },
        "endpoints": {
            "health": "/health",
            "api_status": "/api/status",
            "freq_am": "/api/features/freq_am",
            "multi_features": "/api/features/multi_features",
            "performance_stats": "/api/performance/stats"
        },
        "documentation": "查看交付文档目录获取详细API说明",
        "timestamp": datetime.now().isoformat()
    }

    return create_json_response(info, 200)
