#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API请求验证模块
"""

# 标准库导入
from urllib.parse import urlparse

# 第三方库导入
import pandas as pd
from flask import current_app

# 本地导入
from vibmon_app.utils.exceptions import InvalidDataError, ValidationError



class RequestValidator:
    """请求验证器"""
    


    @staticmethod
    def validate_freq_am_request(data):
        """
        验证频率幅值请求参数

        Args:
            data (dict): 请求数据

        Returns:
            dict: 验证后的数据

        Raises:
            ValidationError: 参数验证失败
        """
        # 必需参数
        if 'equipment_code' not in data:
            raise ValidationError("缺少必需参数: equipment_code")

        if 'rpm' not in data:
            raise ValidationError("缺少必需参数: rpm")

        # 数据源参数（必须提供minio_url）
        minio_url = data.get('minio_url')

        if not minio_url:
            raise ValidationError("必须提供minio_url参数")

        # 验证设备编码
        equipment_code = data['equipment_code']
        predefined_devices = current_app.config.get('PREDEFINED_DEVICES')
        if predefined_devices is None:
            # 如果配置为None，尝试重新加载
            from vibmon_app.config import Config
            predefined_devices = Config.reload_device_config()

        if not predefined_devices or equipment_code not in predefined_devices:
            available_devices = sorted(predefined_devices.keys()) if predefined_devices else []
            raise InvalidDataError(
                f"未知设备编码: {equipment_code}。"
                f"可用设备: {', '.join(available_devices)}"
            )

        # 验证RPM
        try:
            rpm = float(data['rpm'])
            if rpm <= 0:
                raise ValidationError("rpm必须大于0")
        except (ValueError, TypeError):
            raise ValidationError("rpm必须是有效的数字")

        # 验证数据源

        if minio_url:
            # MinIO URL验证，支持多种格式
            if minio_url.startswith('minio://'):
                # minio://格式验证
                try:
                    url_parts = minio_url[8:].split('/', 1)
                    if len(url_parts) < 2 or not url_parts[0] or not url_parts[1]:
                        raise ValidationError("minio_url格式错误，应为: minio://bucket/path/to/file.csv")
                except Exception:
                    raise ValidationError("minio://格式的URL解析错误")
            elif minio_url.startswith(('http://', 'https://')):
                # HTTP URL格式验证
                try:
                    parsed_url = urlparse(minio_url)
                    if not parsed_url.netloc or not parsed_url.path:
                        raise ValidationError("HTTP URL格式错误")

                    # 检查路径是否包含存储桶和对象路径
                    path = parsed_url.path.lstrip('/')
                    if '/' not in path:
                        raise ValidationError("HTTP URL缺少对象路径，应为: http://ip:port/bucket/path/to/file.csv")
                except Exception as e:
                    raise ValidationError(f"HTTP URL格式验证失败: {str(e)}")
            else:
                raise ValidationError("minio_url必须以'minio://'、'http://'或'https://'开头")

        # 验证可选参数 - save_to_minio默认为true
        save_to_minio = data.get('save_to_minio', True)
        if not isinstance(save_to_minio, bool):
            raise ValidationError("save_to_minio必须是布尔值")

        output_bucket = data.get('output_bucket')
        if output_bucket and not isinstance(output_bucket, str):
            raise ValidationError("output_bucket必须是字符串")

        return {
            'minio_url': minio_url,
            'equipment_code': equipment_code,
            'rpm': rpm,
            'save_to_minio': save_to_minio,
            'output_bucket': output_bucket
        }



    @staticmethod
    def validate_multi_features_request(data):
        """
        验证多特征请求参数

        Args:
            data (dict): 请求数据

        Returns:
            dict: 验证后的参数

        Raises:
            InvalidDataError: 验证失败时抛出
        """
        # 必需参数
        if 'equipment_code' not in data:
            raise ValidationError("缺少必需参数: equipment_code")

        # 数据源参数（必须提供minio_url）
        minio_url = data.get('minio_url')

        if not minio_url:
            raise ValidationError("必须提供minio_url参数")

        # 验证设备编码
        equipment_code = data['equipment_code']
        predefined_devices = current_app.config.get('PREDEFINED_DEVICES')
        if predefined_devices is None:
            from vibmon_app.config import Config
            predefined_devices = Config.reload_device_config()

        if not predefined_devices or equipment_code not in predefined_devices:
            available_devices = sorted(predefined_devices.keys()) if predefined_devices else []
            raise InvalidDataError(
                f"未知设备编码: {equipment_code}。"
                f"可用设备: {', '.join(available_devices)}"
            )

        # 验证必需的整数参数
        required_int_params = ['predict_flag', 'early_warning_status', 'alarm_status', 'health_degree_flag']
        for param in required_int_params:
            if param not in data:
                raise ValidationError(f"缺少必需参数: {param}")

        # 验证整数参数
        try:
            predict_flag = int(data['predict_flag'])
            early_warning_status = int(data['early_warning_status'])
            alarm_status = int(data['alarm_status'])
            health_degree_flag = int(data['health_degree_flag'])
        except (ValueError, TypeError) as e:
            raise InvalidDataError(f"参数类型错误，需要整数: {str(e)}")

        # 验证可选的维护周期参数
        mtce_period = data.get('mtce_period', 365)  # 默认365天
        try:
            mtce_period = int(mtce_period)
        except (ValueError, TypeError):
            raise InvalidDataError("mtce_period必须是整数")

        if mtce_period <= 0:
            raise InvalidDataError("mtce_period必须是正整数")

        # 验证标志位参数范围
        if predict_flag not in [0, 1]:
            raise InvalidDataError("predict_flag必须是0或1")

        if health_degree_flag not in [0, 1]:
            raise InvalidDataError("health_degree_flag必须是0或1")

        # 验证状态参数范围
        if early_warning_status < 0 or alarm_status < 0:
            raise InvalidDataError("early_warning_status和alarm_status不能为负数")

        # 验证数据源

        if minio_url:
            # MinIO URL验证，支持多种格式
            if minio_url.startswith('minio://'):
                # minio://格式验证
                try:
                    url_parts = minio_url[8:].split('/', 1)
                    if len(url_parts) < 2 or not url_parts[0] or not url_parts[1]:
                        raise ValidationError("minio_url格式错误，应为: minio://bucket/path/to/file.csv")
                except Exception:
                    raise ValidationError("minio://格式的URL解析错误")
            elif minio_url.startswith(('http://', 'https://')):
                # HTTP URL格式验证
                try:
                    parsed_url = urlparse(minio_url)
                    if not parsed_url.netloc or not parsed_url.path:
                        raise ValidationError("HTTP URL格式错误")

                    # 检查路径是否包含存储桶和对象路径
                    path = parsed_url.path.lstrip('/')
                    if '/' not in path:
                        raise ValidationError("HTTP URL缺少对象路径，应为: http://ip:port/bucket/path/to/file.csv")
                except Exception as e:
                    raise ValidationError(f"HTTP URL格式验证失败: {str(e)}")
            else:
                raise ValidationError("minio_url必须以'minio://'、'http://'或'https://'开头")

        return {
            'minio_url': minio_url,
            'equipment_code': equipment_code,
            'predict_flag': predict_flag,
            'health_degree_flag': health_degree_flag,
            'early_warning_status': early_warning_status,
            'alarm_status': alarm_status,
            'mtce_period': mtce_period
        }


