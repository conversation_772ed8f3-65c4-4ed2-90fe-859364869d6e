#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask应用工厂模块
"""

# 抑制常见警告
from vibmon_app.utils import warning_suppression

import os
import sys
import logging
from logging.handlers import RotatingFileHandler
from flask import Flask
from vibmon_app.config import Config
from vibmon_app.models.ml_models import ModelManager


def create_app(config_class=Config):
    """
    Flask应用工厂函数

    Args:
        config_class: 配置类

    Returns:
        Flask: 配置好的Flask应用实例
    """
    app = Flask(__name__)
    app.config.from_object(config_class)

    # 配置JSON编码，确保中文字符正确显示
    app.config['JSON_AS_ASCII'] = False
    app.config['JSONIFY_MIMETYPE'] = 'application/json; charset=utf-8'

    # 配置日志
    configure_logging(app)
    
    # 初始化模型管理器
    from vibmon_app.models.ml_models import model_manager
    model_manager.init_app(app)

    # 注册蓝图
    from vibmon_app.api.routes import api_bp, root_bp
    app.register_blueprint(api_bp, url_prefix='/api')
    app.register_blueprint(root_bp)  # 根路由，包含健康检查
    
    app.logger.info("[SUCCESS] Flask应用初始化完成")
    return app


def configure_logging(app):
    """
    配置应用日志

    Args:
        app: Flask应用实例
    """
    # 防止重复配置日志处理器
    if hasattr(app.logger, '_configured'):
        return

    # 清除现有的处理器，防止重复日志
    app.logger.handlers.clear()

    # 设置日志级别
    log_level = os.environ.get('LOGLEVEL', 'INFO').upper()
    app.logger.setLevel(getattr(logging, log_level, logging.INFO))

    # 检查是否为开发模式
    is_development = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'

    # 获取日志详细程度设置
    vibmon_log_level = os.environ.get('VIBMON_LOG_LEVEL', 'STANDARD').upper()

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)

    # 设置编码以避免Windows下的Unicode问题
    if hasattr(console_handler.stream, 'reconfigure'):
        try:
            console_handler.stream.reconfigure(encoding='utf-8')
        except (AttributeError, OSError):
            # 某些系统不支持重新配置编码，忽略此错误
            pass

    # 根据日志级别选择格式化器
    if vibmon_log_level == 'SILENT':
        # 静默格式 - 几乎无输出
        formatter = logging.Formatter('%(message)s')
        console_handler.setLevel(logging.ERROR)  # 只显示错误
    elif vibmon_log_level == 'MINIMAL':
        # 最简格式 - 适用于生产环境高频调用
        formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s')
    elif vibmon_log_level == 'STANDARD':
        # 标准格式 - 平衡可读性和性能
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    else:  # DETAILED
        # 详细格式 - 适用于开发调试
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s '
            '[%(pathname)s:%(lineno)d in %(funcName)s()]'
        )

    console_handler.setFormatter(formatter)

    # 将处理器添加到应用的日志记录器中
    app.logger.addHandler(console_handler)

    # 禁用传播到根日志记录器，防止重复输出
    app.logger.propagate = False

    # 标记已配置，防止重复配置
    app.logger._configured = True

    # 开发环境：只输出到控制台，不创建日志文件
    if is_development:
        if vibmon_log_level not in ['SILENT', 'MINIMAL']:  # 静默和最小级别不输出启动信息
            app.logger.info("[DEVELOPMENT] 开发模式 - 日志仅输出到控制台")
        return

    # 生产环境：创建日志文件
    if vibmon_log_level not in ['SILENT', 'MINIMAL']:  # 静默和最小级别不输出启动信息
        app.logger.info("[PRODUCTION] 生产模式 - 启用文件日志记录")

    # 创建logs目录（如果不存在）
    if not os.path.exists('logs'):
        os.makedirs('logs')

    # 优化的文件日志处理器配置
    # 主日志文件 - 5MB轮转，保留20个文件（约100MB总容量）
    file_handler = RotatingFileHandler(
        'logs/vibmon.log',
        maxBytes=5242880,  # 5MB
        backupCount=20,    # 保留20个备份文件
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.INFO)  # 只记录INFO及以上级别
    app.logger.addHandler(file_handler)

    # 错误日志文件处理器 - 2MB轮转，保留10个文件
    error_file_handler = RotatingFileHandler(
        'logs/vibmon_error.log',
        maxBytes=2097152,  # 2MB
        backupCount=10,    # 保留10个备份文件
        encoding='utf-8'
    )
    error_file_handler.setFormatter(formatter)
    error_file_handler.setLevel(logging.ERROR)
    app.logger.addHandler(error_file_handler)

    # API调用统计日志 - 用于监控和分析
    api_stats_handler = RotatingFileHandler(
        'logs/api_stats.log',
        maxBytes=1048576,  # 1MB
        backupCount=30,    # 保留30个备份文件（约30MB）
        encoding='utf-8'
    )
    # 简化的统计格式
    stats_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )
    api_stats_handler.setFormatter(stats_formatter)
    api_stats_handler.setLevel(logging.INFO)

    # 创建专门的API统计logger
    api_stats_logger = logging.getLogger('api_stats')
    api_stats_logger.setLevel(logging.INFO)
    api_stats_logger.addHandler(api_stats_handler)
    api_stats_logger.propagate = False  # 不传播到根logger
