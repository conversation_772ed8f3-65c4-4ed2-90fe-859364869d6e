#!/usr/bin/env python3
"""
MinIO配置管理模块
用于加载和管理MinIO相关配置
"""

import os
import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class MinIOConfig:
    """MinIO配置管理类"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path (str, optional): 配置文件路径
        """
        if config_path is None:
            # 默认配置文件路径
            project_root = Path(__file__).parent.parent.parent
            config_path = project_root / "config" / "minio.yaml"
        
        self.config_path = Path(config_path)
        self._config_cache = None
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if not self.config_path.exists():
                raise FileNotFoundError(f"MinIO配置文件不存在: {self.config_path}")
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self._config_cache = yaml.safe_load(f)
            
            logger.info(f"MinIO配置加载成功: {self.config_path}")
            
            # 验证配置
            self._validate_config()
            
        except Exception as e:
            logger.error(f"MinIO配置加载失败: {str(e)}")
            raise
    
    def _validate_config(self):
        """验证配置文件格式"""
        if not self._config_cache:
            raise ValueError("配置文件为空")
        
        # 检查必要的配置项
        required_sections = ['minio', 'buckets', 'paths', 'csv_format']
        for section in required_sections:
            if section not in self._config_cache:
                raise ValueError(f"配置文件缺少必要部分: {section}")
        
        # 检查MinIO连接配置
        minio_config = self._config_cache['minio']
        required_minio_fields = ['endpoint', 'access_key', 'secret_key']
        for field in required_minio_fields:
            if field not in minio_config:
                raise ValueError(f"MinIO配置缺少必要字段: {field}")
        
        # 检查存储桶配置
        bucket_config = self._config_cache['buckets']
        required_bucket_fields = ['input_bucket', 'output_bucket']
        for field in required_bucket_fields:
            if field not in bucket_config:
                raise ValueError(f"存储桶配置缺少必要字段: {field}")
        
        logger.info("MinIO配置验证通过")
    
    def get_minio_config(self) -> Dict[str, Any]:
        """
        获取MinIO连接配置

        环境变量优先级高于配置文件，支持以下环境变量：
        - MINIO_ENDPOINT: MinIO服务器地址
        - MINIO_ACCESS_KEY: MinIO访问密钥
        - MINIO_SECRET_KEY: MinIO密钥
        - MINIO_SECURE: 是否使用HTTPS (true/false)
        - MINIO_REGION: MinIO区域

        Returns:
            dict: MinIO连接配置
        """
        # 从配置文件获取基础配置
        config = self._config_cache['minio'].copy()

        # 环境变量覆盖配置文件设置
        env_mappings = {
            'MINIO_ENDPOINT': 'endpoint',
            'MINIO_ACCESS_KEY': 'access_key',
            'MINIO_SECRET_KEY': 'secret_key',
            'MINIO_REGION': 'region'
        }

        for env_var, config_key in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value:
                config[config_key] = env_value
                logger.info(f"使用环境变量 {env_var} 覆盖配置")

        # 特殊处理布尔值
        minio_secure = os.getenv('MINIO_SECURE')
        if minio_secure is not None:
            config['secure'] = minio_secure.lower() in ('true', '1', 'yes', 'on')
            logger.info(f"使用环境变量 MINIO_SECURE 覆盖配置")

        return config
    
    def get_bucket_config(self) -> Dict[str, Any]:
        """
        获取存储桶配置

        环境变量优先级高于配置文件，支持以下环境变量：
        - MINIO_INPUT_BUCKET: 输入数据存储桶
        - MINIO_OUTPUT_BUCKET: 输出结果存储桶

        Returns:
            dict: 存储桶配置
        """
        # 从配置文件获取基础配置
        config = self._config_cache['buckets'].copy()

        # 环境变量覆盖配置文件设置
        env_mappings = {
            'MINIO_INPUT_BUCKET': 'input_bucket',
            'MINIO_OUTPUT_BUCKET': 'output_bucket'
        }

        for env_var, config_key in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value:
                config[config_key] = env_value
                logger.info(f"使用环境变量 {env_var} 覆盖配置")

        return config
    
    def get_paths_config(self) -> Dict[str, Any]:
        """
        获取路径配置
        
        Returns:
            dict: 路径配置
        """
        return self._config_cache['paths'].copy()
    
    def get_csv_format(self) -> Dict[str, Any]:
        """
        获取CSV格式配置
        
        Returns:
            dict: CSV格式配置
        """
        return self._config_cache['csv_format'].copy()
    
    def get_processing_config(self) -> Dict[str, Any]:
        """
        获取处理配置
        
        Returns:
            dict: 处理配置
        """
        return self._config_cache.get('processing', {}).copy()
    
    def get_error_handling_config(self) -> Dict[str, Any]:
        """
        获取错误处理配置
        
        Returns:
            dict: 错误处理配置
        """
        return self._config_cache.get('error_handling', {}).copy()
    
    def get_input_bucket(self) -> str:
        """
        获取输入存储桶名称

        支持环境变量覆盖：MINIO_INPUT_BUCKET

        Returns:
            str: 输入存储桶名称
        """
        bucket_config = self.get_bucket_config()
        return bucket_config['input_bucket']

    def get_output_bucket(self) -> str:
        """
        获取输出存储桶名称

        支持环境变量覆盖：MINIO_OUTPUT_BUCKET

        Returns:
            str: 输出存储桶名称
        """
        bucket_config = self.get_bucket_config()
        return bucket_config['output_bucket']
    
    def reload_config(self):
        """重新加载配置文件"""
        self._config_cache = None
        self._load_config()
        logger.info("MinIO配置已重新加载")
    
    def get_config_info(self) -> Dict[str, Any]:
        """
        获取配置文件信息
        
        Returns:
            dict: 配置文件信息
        """
        return {
            'config_path': str(self.config_path),
            'exists': self.config_path.exists(),
            'minio_endpoint': self._config_cache['minio']['endpoint'],
            'input_bucket': self._config_cache['buckets']['input_bucket'],
            'output_bucket': self._config_cache['buckets']['output_bucket'],
            'timezone': self._config_cache['paths']['timezone']
        }
