#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数模块
"""

import os
import warnings
import numpy as np
from flask import current_app


def setup_numpy_warnings():
    """设置NumPy警告处理"""
    if current_app.config.get('SUPPRESS_NUMPY_WARNINGS', True):
        warnings.filterwarnings(
            "ignore",
            message=".*Conversion of an array with ndim > 0 to a scalar.*",
            category=DeprecationWarning
        )


def safe_float_conversion(value):
    """
    安全地将NumPy数组或标量转换为Python float
    兼容NumPy 1.25+的新要求

    Args:
        value: 要转换的值（可能是NumPy数组、标量或Python数值）

    Returns:
        float: 转换后的浮点数
    """
    if hasattr(value, 'item'):
        # NumPy标量或0维数组，使用.item()方法
        return float(value.item())
    elif hasattr(value, '__len__') and len(value) == 1:
        # 长度为1的数组，递归处理第一个元素
        return safe_float_conversion(value[0])
    else:
        # 普通Python数值或其他类型
        return float(value)


def validate_file_path(file_path):
    """
    验证文件路径安全性

    Args:
        file_path (str): 文件路径

    Returns:
        bool: 路径是否安全

    Raises:
        ValueError: 路径不安全时抛出异常
    """
    if not isinstance(file_path, str) or not file_path.strip():
        raise ValueError("文件路径必须是非空字符串")

    # 标准化路径
    normalized_path = os.path.normpath(file_path)

    # 防止路径遍历攻击
    if '..' in normalized_path or normalized_path.startswith('/') or ':' in normalized_path:
        raise ValueError("不安全的文件路径")

    # 检查路径长度
    if len(normalized_path) > 255:
        raise ValueError("文件路径过长")

    # 检查文件扩展名
    allowed_extensions = ['.csv']
    if not any(normalized_path.lower().endswith(ext) for ext in allowed_extensions):
        raise ValueError("不支持的文件类型，仅支持CSV文件")

    return True


def check_file_exists(file_path):
    """
    检查文件是否存在
    
    Args:
        file_path (str): 文件路径
        
    Returns:
        bool: 文件是否存在
        
    Raises:
        FileNotFoundError: 文件不存在时抛出异常
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")
    return True


def validate_csv_columns(df, required_columns):
    """
    验证CSV文件是否包含必要的列
    
    Args:
        df: pandas DataFrame
        required_columns (list): 必需的列名列表
        
    Returns:
        bool: 是否包含所有必需列
        
    Raises:
        ValueError: 缺少必需列时抛出异常
    """
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        raise ValueError(
            f"CSV文件缺少必要的列: {missing_columns}. "
            f"可用列: {list(df.columns)}"
        )
    
    return True


def validate_data_quality(data_arrays, min_samples=100):
    """
    验证数据质量

    Args:
        data_arrays (list): 数据数组列表
        min_samples (int): 最小样本数

    Returns:
        bool: 数据质量是否合格

    Raises:
        ValueError: 数据质量不合格时抛出异常
    """
    if not data_arrays:
        raise ValueError("数据数组列表不能为空")

    for i, data in enumerate(data_arrays):
        # 检查数据是否为空
        if data is None:
            raise ValueError(f"第{i+1}个数据数组为None")

        # 转换为numpy数组进行检查
        data_array = np.asarray(data)

        # 检查数据长度
        if len(data_array) < min_samples:
            raise ValueError(
                f"第{i+1}个数据数组长度不足: 需要至少{min_samples}个点，"
                f"实际只有{len(data_array)}个"
            )

        # 检查NaN值
        if np.any(np.isnan(data_array)):
            raise ValueError(f"第{i+1}个数据数组包含NaN值")

        # 检查无穷大值
        if np.any(np.isinf(data_array)):
            raise ValueError(f"第{i+1}个数据数组包含无穷大值")

        # 检查数据范围（防止异常大的值）
        if np.any(np.abs(data_array) > 1e10):
            raise ValueError(f"第{i+1}个数据数组包含异常大的值")

    return True
