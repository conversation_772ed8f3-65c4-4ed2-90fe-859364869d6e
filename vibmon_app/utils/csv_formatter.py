#!/usr/bin/env python3
"""
CSV格式化模块
用于将freq_am和multi_features的结果格式化为列式CSV
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional

from vibmon_app.utils.minio_config import MinIOConfig

logger = logging.getLogger(__name__)


class CSVFormatter:
    """CSV格式化器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化CSV格式化器
        
        Args:
            config_path (str, optional): 配置文件路径
        """
        self.config = MinIOConfig(config_path)
        self.csv_format = self.config.get_csv_format()
    
    def format_freq_am_result(self, 
                             spectrum_data: Dict[str, Any],
                             time_seconds: List[float],
                             total_waveform: List[float],
                             bearing_analysis: Dict[str, Any]) -> pd.DataFrame:
        """
        格式化freq_am结果为列式CSV
        
        Args:
            spectrum_data (dict): 频谱数据
            time_seconds (list): 时间序列
            total_waveform (list): 总波形数据
            bearing_analysis (dict): 轴承分析结果
            
        Returns:
            pd.DataFrame: 格式化后的数据
        """
        try:
            logger.info("开始格式化freq_am结果为CSV")
            
            # 获取精度配置
            decimal_places = self.csv_format['decimal_places']
            
            # 准备数据字典
            data_dict = {}
            
            # 1. 处理时间序列数据
            max_length = len(time_seconds)
            data_dict['time_seconds'] = [round(t, decimal_places['time']) for t in time_seconds]
            
            # 2. 处理频谱数据
            spectrum_directions = ['horizontal', 'axial', 'vertical', 'total']
            
            for direction in spectrum_directions:
                if direction in spectrum_data:
                    freq_data = spectrum_data[direction].get('frequency', [])
                    amp_data = spectrum_data[direction].get('amplitude', [])
                    
                    # 调整频谱数据长度
                    freq_aligned = self._align_data(freq_data, max_length, decimal_places['frequency'])
                    amp_aligned = self._align_data(amp_data, max_length, decimal_places['amplitude'])
                    
                    data_dict[f'{direction}_spectrum_freq'] = freq_aligned
                    data_dict[f'{direction}_spectrum_amp'] = amp_aligned
                else:
                    # 如果没有该方向的数据，填充空值
                    data_dict[f'{direction}_spectrum_freq'] = [None] * max_length
                    data_dict[f'{direction}_spectrum_amp'] = [None] * max_length
            
            # 3. 处理波形数据
            # 假设total_waveform包含三轴数据，需要重新构造
            samples_per_axis = len(time_seconds)
            if len(total_waveform) >= samples_per_axis:
                waveform_aligned = self._align_data(
                    total_waveform[:samples_per_axis], 
                    max_length, 
                    decimal_places['amplitude']
                )
            else:
                waveform_aligned = self._align_data(
                    total_waveform, 
                    max_length, 
                    decimal_places['amplitude']
                )
            
            data_dict['waveform_time'] = data_dict['time_seconds'].copy()
            data_dict['waveform_amplitude'] = waveform_aligned
            
            # 4. 处理轴承分析数据（仅第一行有值）
            bearing_columns = ['bearing_rotation_freq', 'bearing_outer_fcf', 'bearing_inner_fcf']
            bearing_values = [
                bearing_analysis.get('rotation_frequency_hz', None),
                bearing_analysis.get('recommended_outer_fcf', None),
                bearing_analysis.get('recommended_inner_fcf', None)
            ]
            
            for i, col_name in enumerate(bearing_columns):
                col_data = [None] * max_length
                if bearing_values[i] is not None:
                    col_data[0] = round(bearing_values[i], decimal_places['bearing'])
                data_dict[col_name] = col_data
            
            # 5. 创建DataFrame
            df = pd.DataFrame(data_dict)
            
            # 6. 处理空值
            null_value = self.csv_format['null_value']
            if null_value != "":
                df = df.fillna(null_value)
            
            logger.info(f"freq_am结果格式化完成，数据形状: {df.shape}")
            
            return df
            
        except Exception as e:
            logger.error(f"freq_am结果格式化失败: {str(e)}")
            raise
    
    def format_multi_features_result(self,
                                   features_data: Dict[str, Any],
                                   metadata: Dict[str, Any]) -> pd.DataFrame:
        """
        格式化multi_features结果为列式CSV

        Args:
            features_data (dict): 特征数据
            metadata (dict): 元数据

        Returns:
            pd.DataFrame: 格式化后的数据
        """
        try:
            logger.info("开始格式化multi_features结果为CSV")

            # 获取精度配置
            decimal_places = self.csv_format['decimal_places']

            # 准备数据字典
            data_dict = {}

            # 处理特征数据 - 扁平化嵌套结构
            def flatten_dict(d, parent_key='', sep='_'):
                """递归扁平化字典"""
                items = []
                for k, v in d.items():
                    new_key = f"{parent_key}{sep}{k}" if parent_key else k
                    if isinstance(v, dict):
                        items.extend(flatten_dict(v, new_key, sep=sep).items())
                    elif isinstance(v, list):
                        # 处理列表 - 取第一个值或计算统计量
                        if len(v) > 0:
                            if isinstance(v[0], (int, float)):
                                # 数值列表，计算统计量
                                items.append((f"{new_key}_mean", np.mean(v)))
                                items.append((f"{new_key}_std", np.std(v)))
                                items.append((f"{new_key}_max", np.max(v)))
                                items.append((f"{new_key}_min", np.min(v)))
                            else:
                                # 非数值列表，取第一个值
                                items.append((new_key, str(v[0])))
                        else:
                            items.append((new_key, None))
                    else:
                        items.append((new_key, v))
                return dict(items)

            # 扁平化特征数据
            flattened_features = flatten_dict(features_data)

            # 处理每个特征值
            for feature_name, feature_value in flattened_features.items():
                if isinstance(feature_value, (int, float)):
                    # 根据特征类型选择精度
                    if 'frequency' in feature_name.lower():
                        precision = decimal_places['frequency']
                    elif any(keyword in feature_name.lower() for keyword in ['time', 'duration']):
                        precision = decimal_places['time']
                    else:
                        precision = decimal_places['amplitude']

                    data_dict[feature_name] = [round(feature_value, precision)]
                elif feature_value is None:
                    data_dict[feature_name] = [None]
                else:
                    data_dict[feature_name] = [str(feature_value)]

            # 添加元数据
            for meta_key, meta_value in metadata.items():
                if isinstance(meta_value, (int, float)):
                    data_dict[f'meta_{meta_key}'] = [round(meta_value, decimal_places['bearing'])]
                else:
                    data_dict[f'meta_{meta_key}'] = [str(meta_value)]

            # 创建DataFrame
            df = pd.DataFrame(data_dict)

            # 处理空值
            null_value = self.csv_format['null_value']
            if null_value != "":
                df = df.fillna(null_value)

            logger.info(f"multi_features结果格式化完成，数据形状: {df.shape}")
            logger.info(f"特征列数: {len(df.columns)}")

            return df

        except Exception as e:
            logger.error(f"multi_features结果格式化失败: {str(e)}")
            raise
    
    def _align_data(self, data: List[float], target_length: int, decimal_places: int) -> List[Optional[float]]:
        """
        对齐数据长度
        
        Args:
            data (list): 原始数据
            target_length (int): 目标长度
            decimal_places (int): 小数位数
            
        Returns:
            list: 对齐后的数据
        """
        try:
            if not data:
                return [None] * target_length
            
            # 四舍五入到指定精度
            rounded_data = [round(x, decimal_places) if x is not None else None for x in data]
            
            if len(rounded_data) == target_length:
                return rounded_data
            elif len(rounded_data) < target_length:
                # 数据不足，用None填充
                return rounded_data + [None] * (target_length - len(rounded_data))
            else:
                # 数据过多，截断到目标长度
                return rounded_data[:target_length]
                
        except Exception as e:
            logger.error(f"数据对齐失败: {str(e)}")
            return [None] * target_length
    
    def validate_dataframe(self, df: pd.DataFrame) -> bool:
        """
        验证DataFrame格式
        
        Args:
            df (pd.DataFrame): 要验证的DataFrame
            
        Returns:
            bool: 验证是否通过
        """
        try:
            if df.empty:
                logger.warning("DataFrame为空")
                return False
            
            # 检查必要的列
            required_columns = ['time_seconds']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                logger.error(f"DataFrame缺少必要列: {missing_columns}")
                return False
            
            logger.info("DataFrame格式验证通过")
            return True
            
        except Exception as e:
            logger.error(f"DataFrame验证失败: {str(e)}")
            return False
