#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
警告抑制模块
在应用启动时导入此模块以抑制常见警告
"""

import warnings
import os

# 设置环境变量以抑制警告
os.environ['PYTHONWARNINGS'] = 'ignore::UserWarning:sklearn,ignore::DeprecationWarning:pkg_resources'

# 全局警告过滤器
def setup_global_warning_filters():
    """设置全局警告过滤器"""
    # sklearn相关警告
    warnings.filterwarnings('ignore', category=UserWarning, module='sklearn')
    warnings.filterwarnings('ignore', message='.*InconsistentVersionWarning.*')
    warnings.filterwarnings('ignore', message='.*version.*when using version.*')
    
    # pkg_resources弃用警告
    warnings.filterwarnings('ignore', category=DeprecationWarning, module='pkg_resources')
    warnings.filterwarnings('ignore', message='.*pkg_resources is deprecated.*')
    
    # 其他常见警告
    warnings.filterwarnings('ignore', category=FutureWarning, module='sklearn')
    warnings.filterwarnings('ignore', category=DeprecationWarning, module='numpy')

# 自动执行
setup_global_warning_filters()
