#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志工具模块
"""

import os
import json
import time
import logging
from functools import wraps
from flask import request, current_app, g
from datetime import datetime

# 日志级别配置
LOG_LEVELS = {
    'SILENT': 0,     # 几乎无日志输出，只记录错误
    'MINIMAL': 1,    # 只记录API请求/响应和错误
    'STANDARD': 2,   # 记录关键处理步骤
    'DETAILED': 3,   # 记录所有详细信息（当前默认）
}

# 从环境变量获取日志级别，默认为STANDARD
CURRENT_LOG_LEVEL = LOG_LEVELS.get(
    os.environ.get('VIBMON_LOG_LEVEL', 'STANDARD').upper(),
    LOG_LEVELS['STANDARD']
)

def get_api_stats_logger():
    """获取API统计日志记录器"""
    return logging.getLogger('api_stats')


def should_log_detail():
    """判断是否应该记录详细日志"""
    return CURRENT_LOG_LEVEL >= LOG_LEVELS['DETAILED']


def should_log_processing():
    """判断是否应该记录处理步骤"""
    return CURRENT_LOG_LEVEL >= LOG_LEVELS['STANDARD']


def log_api_call(func):
    """
    API调用日志装饰器
    记录请求参数、响应状态、执行时间等信息
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        # 记录开始时间
        start_time = time.time()
        request_id = f"{int(start_time * 1000000)}"  # 生成请求ID
        
        # 获取请求信息
        method = request.method
        endpoint = request.endpoint
        url = request.url
        remote_addr = request.remote_addr
        user_agent = request.headers.get('User-Agent', 'Unknown')
        
        # 获取请求参数
        request_data = {}
        
        # JSON数据
        if request.is_json:
            try:
                request_data['json'] = request.get_json()
            except Exception as e:
                request_data['json_error'] = str(e)
        
        # URL参数
        if request.args:
            request_data['args'] = dict(request.args)
        
        # 表单数据
        if request.form:
            request_data['form'] = dict(request.form)
        
        # 文件上传
        if request.files:
            request_data['files'] = list(request.files.keys())
        
        # 根据日志级别记录请求信息
        if CURRENT_LOG_LEVEL >= LOG_LEVELS['MINIMAL']:
            # MINIMAL级别：只记录基本请求信息
            if CURRENT_LOG_LEVEL == LOG_LEVELS['MINIMAL']:
                current_app.logger.info(f"[API] {method} {endpoint} | ID: {request_id}")
            else:
                # STANDARD及以上级别：记录关键参数
                key_params = extract_key_params(request_data)
                current_app.logger.info(
                    f"[API_REQUEST] {method} {endpoint} | "
                    f"RequestID: {request_id} | "
                    f"IP: {remote_addr} | "
                    f"KeyParams: {json.dumps(key_params, ensure_ascii=False, default=str)}"
                )
        # SILENT级别：不记录请求日志

        # 详细日志级别记录完整参数
        if should_log_detail():
            filtered_params = filter_sensitive_data(request_data)
            current_app.logger.info(
                f"[API_PARAMS_FULL] {method} {endpoint} | "
                f"RequestID: {request_id} | "
                f"FullParams: {json.dumps(filtered_params, ensure_ascii=False, default=str, indent=2)}"
            )
        
        # 存储请求ID到g对象，供其他地方使用
        g.request_id = request_id
        g.start_time = start_time
        
        try:
            # 执行原函数
            result = func(*args, **kwargs)
            
            # 计算执行时间
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            # 获取响应状态码
            if isinstance(result, tuple):
                response_data, status_code = result
            else:
                response_data = result
                status_code = 200
            
            # 根据日志级别记录响应信息
            if CURRENT_LOG_LEVEL >= LOG_LEVELS['MINIMAL']:
                if CURRENT_LOG_LEVEL == LOG_LEVELS['MINIMAL']:
                    # MINIMAL级别：极简响应日志
                    current_app.logger.info(
                        f"[API] {method} {endpoint} | "
                        f"ID: {request_id} | "
                        f"{duration_ms:.0f}ms | "
                        f"{status_code}"
                    )
                else:
                    # STANDARD及以上级别：详细响应日志
                    current_app.logger.info(
                        f"[API_RESPONSE] {method} {endpoint} | "
                        f"RequestID: {request_id} | "
                        f"Status: {status_code} | "
                        f"Duration: {duration_ms:.2f}ms"
                    )

                    # 记录API统计信息到专门的统计日志
                    stats_logger = get_api_stats_logger()
                    stats_logger.info(
                        f"API_CALL|{endpoint}|{status_code}|{duration_ms:.2f}ms|{remote_addr}|{request_id}"
                    )
            # SILENT级别：不记录成功响应日志
            
            return result
            
        except Exception as e:
            # 计算执行时间
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            # 错误日志始终记录（所有级别）
            if CURRENT_LOG_LEVEL == LOG_LEVELS['MINIMAL']:
                # MINIMAL级别：简化错误日志
                current_app.logger.error(
                    f"[ERROR] {method} {endpoint} | "
                    f"ID: {request_id} | "
                    f"{str(e)[:50]}..."  # 截断错误信息
                )
            else:
                # STANDARD及以上级别：详细错误日志
                current_app.logger.error(
                    f"[API_ERROR] {method} {endpoint} | "
                    f"RequestID: {request_id} | "
                    f"Error: {str(e)} | "
                    f"Duration: {duration_ms:.2f}ms",
                    exc_info=should_log_detail()  # 只在详细级别记录堆栈跟踪
                )

                # 记录错误统计信息
                stats_logger = get_api_stats_logger()
                stats_logger.error(
                    f"API_ERROR|{endpoint}|500|{duration_ms:.2f}ms|{remote_addr}|{request_id}|{str(e)}"
                )
            
            # 重新抛出异常
            raise
    
    return wrapper


def log_request_params(params_dict, operation_name="操作"):
    """
    记录请求参数的详细信息

    Args:
        params_dict (dict): 参数字典
        operation_name (str): 操作名称
    """
    request_id = getattr(g, 'request_id', 'unknown')

    # 在所有日志级别都记录关键参数
    key_params = {}
    # 提取常见的关键参数
    key_fields = [
        'equipment_code', 'data_path', 'predict_flag', 'health_degree_flag',
        'operation', 'action', 'type', 'mode', 'id', 'name'
    ]

    for field in key_fields:
        if field in params_dict:
            key_params[field] = params_dict[field]

    # 记录关键参数
    current_app.logger.info(
        f"[PARAMS] {operation_name} | "
        f"RequestID: {request_id} | "
        f"关键参数: {json.dumps(key_params, ensure_ascii=False, default=str)}"
    )

    # 在详细日志级别记录完整参数
    if should_log_detail():
        # 过滤敏感信息
        filtered_params = filter_sensitive_data(params_dict)

        current_app.logger.info(
            f"[PARAMS_FULL] {operation_name} | "
            f"RequestID: {request_id} | "
            f"详细参数: {json.dumps(filtered_params, ensure_ascii=False, default=str, indent=2)}"
        )


def log_processing_step(step_name, details=None, force_log=False):
    """
    记录处理步骤

    Args:
        step_name (str): 步骤名称
        details (dict): 详细信息
        force_log (bool): 强制记录，忽略日志级别
    """
    # 检查是否应该记录处理步骤
    if not force_log and not should_log_processing():
        return

    request_id = getattr(g, 'request_id', 'unknown')

    log_message = f"[PROCESSING] {step_name} | RequestID: {request_id}"

    # 只在详细级别记录详情
    if details and should_log_detail():
        filtered_details = filter_sensitive_data(details)
        log_message += f" | 详情: {json.dumps(filtered_details, ensure_ascii=False, default=str)}"

    current_app.logger.info(log_message)


def log_result_summary(result_data, operation_name="操作"):
    """
    记录结果摘要

    Args:
        result_data (dict): 结果数据
        operation_name (str): 操作名称
    """
    # 只在标准级别以上记录结果摘要
    if not should_log_processing():
        return

    request_id = getattr(g, 'request_id', 'unknown')

    # 提取关键结果信息
    summary = extract_result_summary(result_data)

    # 简化摘要信息
    simplified_summary = {
        'status': summary.get('status', 'unknown'),
        'key_metrics': {}
    }

    # 只保留关键指标
    key_fields = ['predict_result', 'health_degree', 'rms_values_total', 'total_amplitude']
    for field in key_fields:
        if field in summary:
            simplified_summary['key_metrics'][field] = summary[field]

    current_app.logger.info(
        f"[RESULT] {operation_name}完成 | "
        f"RequestID: {request_id} | "
        f"结果摘要: {json.dumps(simplified_summary, ensure_ascii=False, default=str)}"
    )


def extract_key_params(request_data):
    """
    提取关键参数信息，用于最小日志级别

    Args:
        request_data (dict): 完整的请求数据

    Returns:
        dict: 关键参数信息
    """
    if not isinstance(request_data, dict):
        return {}

    key_params = {}

    # 从JSON数据中提取关键参数
    if 'json' in request_data and isinstance(request_data['json'], dict):
        json_data = request_data['json']
        # 提取常见的关键参数
        key_fields = [
            'equipment_code', 'data_path', 'predict_flag', 'health_degree_flag',
            'operation', 'action', 'type', 'mode', 'id', 'name'
        ]

        for field in key_fields:
            if field in json_data:
                key_params[field] = json_data[field]

        # 如果没有找到关键字段，显示所有字段名
        if not key_params and json_data:
            key_params['fields'] = list(json_data.keys())

    # 从URL参数中提取
    if 'args' in request_data:
        key_params['url_params'] = request_data['args']

    # 从表单数据中提取关键字段
    if 'form' in request_data:
        form_data = request_data['form']
        for field in ['equipment_code', 'action', 'type']:
            if field in form_data:
                key_params[field] = form_data[field]

    # 文件上传信息
    if 'files' in request_data:
        key_params['uploaded_files'] = request_data['files']

    return key_params


def filter_sensitive_data(data):
    """
    过滤敏感数据

    Args:
        data: 要过滤的数据

    Returns:
        过滤后的数据
    """
    if not isinstance(data, dict):
        return data

    sensitive_keys = ['password', 'token', 'secret', 'key', 'auth']
    filtered = {}

    for key, value in data.items():
        if any(sensitive_key in key.lower() for sensitive_key in sensitive_keys):
            filtered[key] = "***HIDDEN***"
        elif isinstance(value, dict):
            filtered[key] = filter_sensitive_data(value)
        elif isinstance(value, list) and len(value) > 100:
            # 对于长数组，只显示长度和前几个元素
            filtered[key] = f"[Array length: {len(value)}, first 3: {value[:3]}...]"
        else:
            filtered[key] = value

    return filtered


def extract_result_summary(result_data):
    """
    提取结果摘要信息
    
    Args:
        result_data: 结果数据
        
    Returns:
        dict: 摘要信息
    """
    if not isinstance(result_data, dict):
        return {"type": type(result_data).__name__}
    
    summary = {}
    
    # 提取关键字段
    if 'result' in result_data:
        result = result_data['result']
        if isinstance(result, dict):
            # 统计数据点数量
            for key, value in result.items():
                if isinstance(value, list):
                    summary[f"{key}_count"] = len(value)
                elif isinstance(value, (int, float)):
                    summary[key] = value
                elif isinstance(value, dict):
                    # 对于嵌套字典，提取数值类型的值
                    for sub_key, sub_value in value.items():
                        if isinstance(sub_value, (int, float)):
                            summary[f"{key}_{sub_key}"] = sub_value
    
    # 添加状态信息
    if 'status' in result_data:
        summary['status'] = result_data['status']
    
    return summary
