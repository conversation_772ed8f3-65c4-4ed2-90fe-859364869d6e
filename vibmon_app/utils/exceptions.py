#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义异常模块
"""


class VibrationAnalysisError(Exception):
    """振动分析基础异常类"""
    pass


class ModelNotLoadedError(VibrationAnalysisError):
    """模型未加载异常"""
    pass


class InvalidDataError(VibrationAnalysisError):
    """无效数据异常"""
    pass


class ValidationError(VibrationAnalysisError):
    """参数验证异常"""
    pass


class FeatureExtractionError(VibrationAnalysisError):
    """特征提取异常"""
    pass


class PredictionError(VibrationAnalysisError):
    """预测异常"""
    pass


class FileProcessingError(VibrationAnalysisError):
    """文件处理异常"""
    pass
