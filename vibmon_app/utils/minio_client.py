#!/usr/bin/env python3
"""
MinIO客户端模块
用于处理MinIO文件的读取和写入操作
"""

# 标准库导入
import io
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Optional, Tuple, Dict, Any
from urllib.parse import urlparse

# 第三方库导入
import pandas as pd
import requests

try:
    from minio import Minio
    from minio.error import S3Error
except ImportError:
    raise ImportError("请安装minio库: pip install minio")

# 本地导入
from vibmon_app.utils.minio_config import MinIOConfig

logger = logging.getLogger(__name__)


class MinIOClient:
    """MinIO客户端类"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化MinIO客户端
        
        Args:
            config_path (str, optional): 配置文件路径
        """
        self.config = MinIOConfig(config_path)
        self.client = None
        self._connect()
    
    def _connect(self):
        """建立MinIO连接"""
        try:
            minio_config = self.config.get_minio_config()
            
            # 解析endpoint，移除协议前缀
            endpoint = minio_config['endpoint']
            if endpoint.startswith('http://'):
                endpoint = endpoint[7:]
                secure = False
            elif endpoint.startswith('https://'):
                endpoint = endpoint[8:]
                secure = True
            else:
                secure = minio_config.get('secure', False)
            
            # 移除末尾的斜杠
            endpoint = endpoint.rstrip('/')
            
            self.client = Minio(
                endpoint=endpoint,
                access_key=minio_config['access_key'],
                secret_key=minio_config['secret_key'],
                secure=secure,
                region=minio_config.get('region', 'us-east-1')
            )
            
            logger.info(f"MinIO客户端连接成功: {endpoint}")
            
        except Exception as e:
            logger.error(f"MinIO连接失败: {str(e)}")
            raise
    
    def parse_minio_url(self, minio_url: str) -> Tuple[str, str]:
        """
        解析MinIO URL，支持多种格式

        Args:
            minio_url (str): MinIO URL，支持以下格式：
                1. minio://bucket/path/to/file.csv
                2. http://ip:port/bucket/path/to/file.csv
                3. http://ip:port/bucket/path/to/file.csv?签名参数

        Returns:
            tuple: (bucket_name, object_path)
        """
        try:
            if minio_url.startswith('minio://'):
                # 原有的minio://格式
                url_without_protocol = minio_url[8:]
                parts = url_without_protocol.split('/', 1)
                if len(parts) < 2:
                    raise ValueError("MinIO URL格式错误，缺少对象路径")

                bucket_name = parts[0]
                object_path = parts[1]

            elif minio_url.startswith(('http://', 'https://')):
                # HTTP URL格式解析
                from urllib.parse import urlparse

                parsed_url = urlparse(minio_url)
                path = parsed_url.path

                # 移除开头的斜杠
                if path.startswith('/'):
                    path = path[1:]

                # 分割bucket和对象路径
                parts = path.split('/', 1)
                if len(parts) < 2:
                    raise ValueError("HTTP URL格式错误，缺少对象路径")

                bucket_name = parts[0]
                object_path = parts[1]

            else:
                raise ValueError("URL格式不支持，请使用 minio:// 或 http:// 格式")

            return bucket_name, object_path

        except Exception as e:
            logger.error(f"MinIO URL解析失败: {str(e)}")
            raise
    
    def read_csv_from_minio(self, minio_url: str) -> pd.DataFrame:
        """
        从MinIO读取CSV文件，支持多种URL格式

        Args:
            minio_url (str): MinIO文件URL，支持：
                1. minio://bucket/path/to/file.csv
                2. http://ip:port/bucket/path/to/file.csv
                3. 带签名的预签名URL

        Returns:
            pd.DataFrame: CSV数据
        """
        try:
            if minio_url.startswith(('http://', 'https://')):
                # 直接使用HTTP URL读取（支持预签名URL）
                logger.info(f"使用HTTP URL读取文件: {minio_url}")

                response = requests.get(minio_url, timeout=60)
                response.raise_for_status()

                # 转换为DataFrame
                csv_content = response.content
                df = pd.read_csv(io.BytesIO(csv_content))

                logger.info(f"成功通过HTTP读取CSV文件，数据形状: {df.shape}")

            else:
                # 使用MinIO客户端读取
                bucket_name, object_path = self.parse_minio_url(minio_url)

                logger.info(f"从MinIO读取文件: {bucket_name}/{object_path}")

                # 检查存储桶是否存在
                if not self.client.bucket_exists(bucket_name):
                    raise ValueError(f"存储桶不存在: {bucket_name}")

                # 读取文件
                response = self.client.get_object(bucket_name, object_path)

                # 转换为DataFrame
                csv_data = response.read()
                df = pd.read_csv(io.BytesIO(csv_data))

                logger.info(f"成功读取CSV文件，数据形状: {df.shape}")

                # 清理资源
                response.close()
                response.release_conn()

            return df

        except requests.exceptions.RequestException as e:
            logger.error(f"HTTP请求错误: {str(e)}")
            raise ValueError(f"无法通过HTTP读取文件: {str(e)}")
        except S3Error as e:
            logger.error(f"MinIO S3错误: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"读取CSV文件失败: {str(e)}")
            raise
    
    def save_csv_to_minio(self, data: pd.DataFrame, bucket_name: str, object_path: str) -> Dict[str, Any]:
        """
        保存CSV文件到MinIO
        
        Args:
            data (pd.DataFrame): 要保存的数据
            bucket_name (str): 存储桶名称
            object_path (str): 对象路径
            
        Returns:
            dict: 保存结果信息
        """
        try:
            logger.info(f"保存CSV文件到MinIO: {bucket_name}/{object_path}")
            
            # 检查并创建存储桶
            if not self.client.bucket_exists(bucket_name):
                if self.config.get_bucket_config().get('allow_create_bucket', False):
                    self.client.make_bucket(bucket_name)
                    logger.info(f"创建存储桶: {bucket_name}")
                else:
                    raise ValueError(f"存储桶不存在且不允许自动创建: {bucket_name}")
            
            # 转换DataFrame为CSV字节流
            csv_format = self.config.get_csv_format()
            csv_buffer = io.StringIO()
            
            data.to_csv(
                csv_buffer,
                index=False,
                encoding=csv_format['encoding'],
                sep=csv_format['separator'],
                header=csv_format['include_header']
            )
            
            csv_bytes = csv_buffer.getvalue().encode(csv_format['encoding'])
            csv_buffer.close()
            
            # 上传文件
            result = self.client.put_object(
                bucket_name=bucket_name,
                object_name=object_path,
                data=io.BytesIO(csv_bytes),
                length=len(csv_bytes),
                content_type='text/csv'
            )
            
            # 构造返回信息
            # 生成真实的HTTP URL而不是minio://格式
            minio_config = self.config.get_minio_config()
            endpoint = minio_config['endpoint'].rstrip('/')
            http_url = f"{endpoint}/{bucket_name}/{object_path}"

            file_info = {
                'bucket_name': bucket_name,
                'object_path': object_path,
                'minio_url': http_url,  # 使用真实的HTTP URL
                'file_size_bytes': len(csv_bytes),
                'etag': result.etag,
                'upload_time': datetime.now().isoformat()
            }
            
            logger.info(f"CSV文件保存成功，大小: {len(csv_bytes)} 字节")
            
            return file_info
            
        except S3Error as e:
            logger.error(f"MinIO S3错误: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"保存CSV文件失败: {str(e)}")
            raise
    
    def generate_output_path(self, device_id: str, bucket_name: str) -> str:
        """
        生成输出文件路径
        
        Args:
            device_id (str): 设备ID
            bucket_name (str): 存储桶名称
            
        Returns:
            str: 生成的文件路径
        """
        try:
            import pytz
            
            # 获取配置
            paths_config = self.config.get_paths_config()
            timezone = pytz.timezone(paths_config['timezone'])
            
            # 获取当前时间
            now = datetime.now(timezone)
            timestamp = int(now.timestamp() * 1000)  # 毫秒时间戳
            
            # 构造路径参数
            path_params = {
                'bucket': bucket_name,
                'year': now.strftime('%Y'),
                'month': now.strftime('%m'),
                'date': now.strftime('%d'),
                'hour': now.strftime('%H'),
                'device_id': device_id,
                'timestamp': timestamp
            }
            
            # 生成路径
            path_pattern = paths_config['path_pattern']
            object_path = path_pattern.format(**path_params)
            
            # 移除bucket前缀，因为bucket会单独处理
            if object_path.startswith(f"{bucket_name}/"):
                object_path = object_path[len(f"{bucket_name}/"):]
            
            logger.info(f"生成输出路径: {object_path}")
            
            return object_path
            
        except Exception as e:
            logger.error(f"生成输出路径失败: {str(e)}")
            raise
    
    def test_connection(self) -> bool:
        """
        测试MinIO连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            # 尝试列出存储桶
            buckets = self.client.list_buckets()
            logger.info(f"MinIO连接测试成功，发现 {len(buckets)} 个存储桶")
            return True
            
        except Exception as e:
            logger.error(f"MinIO连接测试失败: {str(e)}")
            return False
