#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速CSV读取器
针对高频调用场景优化的CSV文件读取模块
"""

import pandas as pd
import numpy as np
import os
from typing import Tuple, Optional
from flask import current_app

class FastCSVReader:
    """快速CSV读取器类"""

    def __init__(self):
        pass
    
    def load_csv_fast(self, file_path: str, max_samples: Optional[int] = None, include_time: bool = False) -> Tuple:
        """
        快速加载CSV文件

        优化特性：
        1. 使用C引擎解析CSV
        2. 指定数据类型减少内存分配
        3. 分块读取大文件
        4. 向量化数据处理

        Args:
            file_path (str): CSV文件路径
            max_samples (int, optional): 最大样本数
            include_time (bool, optional): 是否包含时间数据，默认False

        Returns:
            tuple: 如果include_time=False，返回(水平振动数据, 轴向振动数据, 垂直振动数据, 采样率)
                  如果include_time=True，返回(水平振动数据, 轴向振动数据, 垂直振动数据, 时间数据, 采样率)
            
        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 数据格式错误
        """
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"CSV文件不存在: {file_path}")
        
        try:
            # 使用优化的pandas读取参数
            df = pd.read_csv(
                file_path,
                engine='c',  # 使用C引擎，速度更快
                dtype=np.float64,  # 指定数据类型
                na_values=['', 'NaN', 'NULL'],  # 处理缺失值
                skip_blank_lines=True,  # 跳过空行
                low_memory=False  # 一次性读取，避免数据类型推断
            )
            
            # 检查数据格式
            if df.empty:
                raise ValueError("CSV文件为空")
            
            # 检查必要的振动数据列
            required_columns = ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"CSV文件缺少必要列: {missing_columns}")

            # 如果需要时间数据，检查time_seconds列
            if include_time and 'time_seconds' not in df.columns:
                raise ValueError("CSV文件缺少time_seconds列")

            # 按列名提取振动数据
            data_h_series = df['horizontal_vibration']
            data_a_series = df['axial_vibration']
            data_v_series = df['vertical_vibration']

            # 如果需要，提取时间数据
            time_series = None
            if include_time:
                time_series = df['time_seconds']

            # 限制样本数量
            if max_samples and len(data_h_series) > max_samples:
                data_h_series = data_h_series[:max_samples]
                data_a_series = data_a_series[:max_samples]
                data_v_series = data_v_series[:max_samples]
                if include_time:
                    time_series = time_series[:max_samples]

            # 检查数据有效性并处理NaN值
            data_h = data_h_series.ffill().fillna(0).tolist()
            data_a = data_a_series.ffill().fillna(0).tolist()
            data_v = data_v_series.ffill().fillna(0).tolist()

            # 处理时间数据
            time_seconds = None
            if include_time:
                time_seconds = time_series.ffill().fillna(0).tolist()
            
            # 计算采样率（默认12000Hz）
            sampling_rate = 12000.0
            
            # 记录读取信息
            if current_app:
                time_info = f" | 时间数据: {'是' if include_time else '否'}" if include_time else ""
                current_app.logger.info(
                    f"[FAST_CSV] 文件: {os.path.basename(file_path)} | "
                    f"样本数: {len(data_h)} | 采样率: {sampling_rate}Hz{time_info}"
                )

            # 根据是否需要时间数据返回不同的结果
            if include_time:
                return data_h, data_a, data_v, time_seconds, sampling_rate
            else:
                return data_h, data_a, data_v, sampling_rate
            
        except pd.errors.EmptyDataError:
            raise ValueError("CSV文件为空或格式错误")
        except pd.errors.ParserError as e:
            raise ValueError(f"CSV文件解析错误: {str(e)}")
        except Exception as e:
            raise ValueError(f"读取CSV文件失败: {str(e)}")
    
    def load_csv_chunked(self, file_path: str, chunk_size: int = 10000, max_samples: Optional[int] = None) -> Tuple[list, list, list, float]:
        """
        分块读取大型CSV文件
        
        Args:
            file_path (str): CSV文件路径
            chunk_size (int): 分块大小
            max_samples (int, optional): 最大样本数
            
        Returns:
            tuple: (水平振动数据, 轴向振动数据, 垂直振动数据, 采样率)
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"CSV文件不存在: {file_path}")
        
        try:
            data_chunks = []
            total_samples = 0
            
            # 分块读取
            for chunk in pd.read_csv(
                file_path,
                engine='c',
                dtype=np.float64,
                chunksize=chunk_size,
                na_values=['', 'NaN', 'NULL'],
                skip_blank_lines=True
            ):
                # 检查是否达到最大样本数
                if max_samples and total_samples >= max_samples:
                    break
                
                # 获取前三列
                if chunk.shape[1] < 3:
                    raise ValueError(f"CSV文件列数不足，需要至少3列，实际{chunk.shape[1]}列")
                
                chunk_data = chunk.iloc[:, :3].values
                
                # 处理NaN值（兼容新版pandas）
                if np.any(np.isnan(chunk_data)):
                    df_temp = pd.DataFrame(chunk_data)
                    chunk_data = df_temp.ffill().fillna(0).values
                
                # 限制样本数
                if max_samples:
                    remaining_samples = max_samples - total_samples
                    if len(chunk_data) > remaining_samples:
                        chunk_data = chunk_data[:remaining_samples]
                
                data_chunks.append(chunk_data)
                total_samples += len(chunk_data)
            
            if not data_chunks:
                raise ValueError("CSV文件为空或无有效数据")
            
            # 合并所有块
            data_array = np.vstack(data_chunks)
            
            # 分离三轴数据
            data_h = data_array[:, 0].tolist()
            data_a = data_array[:, 1].tolist()
            data_v = data_array[:, 2].tolist()
            
            sampling_rate = 12000.0
            
            if current_app:
                current_app.logger.info(
                    f"[FAST_CSV_CHUNKED] 文件: {os.path.basename(file_path)} | "
                    f"样本数: {len(data_h)} | 分块数: {len(data_chunks)}"
                )
            
            return data_h, data_a, data_v, sampling_rate
            
        except Exception as e:
            raise ValueError(f"分块读取CSV文件失败: {str(e)}")
    
    def get_file_info(self, file_path: str) -> dict:
        """
        获取CSV文件信息
        
        Args:
            file_path (str): CSV文件路径
            
        Returns:
            dict: 文件信息
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"CSV文件不存在: {file_path}")
        
        try:
            # 只读取前几行来获取信息
            sample_df = pd.read_csv(file_path, nrows=5, engine='c')
            
            # 获取文件大小
            file_size = os.path.getsize(file_path)
            
            # 估算总行数（基于文件大小）
            if len(sample_df) > 0:
                avg_row_size = file_size / (len(sample_df) + 1)  # +1 for header
                estimated_rows = int(file_size / avg_row_size) - 1  # -1 for header
            else:
                estimated_rows = 0
            
            return {
                'file_path': file_path,
                'file_size_bytes': file_size,
                'file_size_mb': round(file_size / (1024 * 1024), 2),
                'columns': list(sample_df.columns),
                'column_count': len(sample_df.columns),
                'estimated_rows': estimated_rows,
                'sample_data': sample_df.head(3).to_dict('records') if not sample_df.empty else []
            }
            
        except Exception as e:
            raise ValueError(f"获取文件信息失败: {str(e)}")


# 创建全局实例
fast_csv_reader = FastCSVReader()
