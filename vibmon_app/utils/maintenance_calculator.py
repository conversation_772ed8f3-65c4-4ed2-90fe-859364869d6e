#!/usr/bin/env python3
"""
维护建议计算模块
"""

# 第三方库导入
import numpy as np


class MaintenanceCalculator:
    """维护建议计算器"""
    
    @staticmethod
    def calculate_remaining_life(health_degree, growth_rate=0.25, midpoint=79, mtce_period=365):
        """
        计算剩余使用寿命
        
        Args:
            health_degree (float): 健康度 (0-100)
            growth_rate (float): 增长率，默认0.25
            midpoint (float): 中点值，默认79
            mtce_period (int): 维护周期（天），默认365
            
        Returns:
            int: 建议维护时间（天）
        """
        # 确保健康度在0-100范围内
        health = np.clip(health_degree, 0, 100)
        
        # 计算剩余使用寿命
        # 使用Sigmoid函数：健康度越高，维护建议时间越长
        x = 100 - health  # 转换为"损坏程度"
        sigmoid_value = 1 / (1 + np.exp(growth_rate * (x - (100 - midpoint))))
        remaining_useful_life = mtce_period * sigmoid_value

        # 返回整数天数，最小为1天（即使设备很差也给1天缓冲）
        return max(1, round(remaining_useful_life))
    
    @staticmethod
    def calculate_maintenance_advice(health_degree, health_degree_flag, mtce_period=365):
        """
        计算维护建议
        
        Args:
            health_degree (float): 健康度
            health_degree_flag (int): 是否计算健康度标志 (0/1)
            mtce_period (int): 维护周期（天）
            
        Returns:
            int or None: 建议维护时间（天），如果不计算则返回None
        """
        if health_degree_flag == 1 and health_degree is not None:
            return MaintenanceCalculator.calculate_remaining_life(
                health_degree=health_degree,
                mtce_period=mtce_period
            )
        else:
            return None
    
    @staticmethod
    def get_maintenance_status(remaining_days):
        """
        获取维护状态描述
        
        Args:
            remaining_days (int): 剩余天数
            
        Returns:
            str: 维护状态描述
        """
        if remaining_days is None:
            return "未计算"
        elif remaining_days <= 7:
            return "紧急维护"
        elif remaining_days <= 30:
            return "近期维护"
        elif remaining_days <= 90:
            return "计划维护"
        else:
            return "正常运行"
    
    @staticmethod
    def get_maintenance_priority(remaining_days):
        """
        获取维护优先级
        
        Args:
            remaining_days (int): 剩余天数
            
        Returns:
            str: 维护优先级 (high/medium/low/normal)
        """
        if remaining_days is None:
            return "normal"
        elif remaining_days <= 7:
            return "high"
        elif remaining_days <= 30:
            return "medium"
        elif remaining_days <= 90:
            return "low"
        else:
            return "normal"
