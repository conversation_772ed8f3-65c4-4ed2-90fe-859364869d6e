#!/usr/bin/env python3
"""
配置加载器模块
用于加载和管理YAML配置文件
"""

import os
import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class DeviceConfigLoader:
    """设备配置加载器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置加载器

        Args:
            config_path (str, optional): 配置文件路径，默认为项目根目录下的config/devices.yaml
        """
        if config_path is None:
            # 优先使用环境变量
            config_path = os.environ.get('CONFIG_PATH')

            if config_path is None:
                # 获取项目根目录
                project_root = Path(__file__).parent.parent.parent
                config_path = project_root / "config" / "devices.yaml"

        self.config_path = Path(config_path)
        self._config_cache = None
        self._last_modified = None
        
    def _load_yaml_file(self) -> Dict[str, Any]:
        """
        加载YAML配置文件
        
        Returns:
            dict: 配置数据
            
        Raises:
            FileNotFoundError: 配置文件不存在
            yaml.YAMLError: YAML格式错误
        """
        if not self.config_path.exists():
            raise FileNotFoundError(f"设备配置文件不存在: {self.config_path}")
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            if not config:
                raise ValueError("配置文件为空")
                
            return config
            
        except yaml.YAMLError as e:
            raise yaml.YAMLError(f"YAML配置文件格式错误: {str(e)}")
        except Exception as e:
            raise Exception(f"读取配置文件失败: {str(e)}")
    
    def _validate_config(self, config: Dict[str, Any]) -> bool:
        """
        验证配置文件格式
        
        Args:
            config (dict): 配置数据
            
        Returns:
            bool: 验证是否通过
            
        Raises:
            ValueError: 配置格式错误
        """
        # 检查必要的顶级字段
        if 'devices' not in config:
            raise ValueError("配置文件缺少 'devices' 字段")
        
        devices = config['devices']
        if not isinstance(devices, dict):
            raise ValueError("'devices' 字段必须是字典格式")
        
        # 获取验证规则
        validation_rules = config.get('validation', {})
        required_fields = validation_rules.get('required_fields', ['outer_coefficient', 'inner_coefficient'])
        
        # 验证每个设备配置
        for device_code, device_config in devices.items():
            if not isinstance(device_config, dict):
                raise ValueError(f"设备 {device_code} 的配置必须是字典格式")
            
            # 检查必要字段
            for field in required_fields:
                if field not in device_config:
                    raise ValueError(f"设备 {device_code} 缺少必要字段: {field}")
            
            # 验证系数范围
            outer_coeff = device_config.get('outer_coefficient')
            inner_coeff = device_config.get('inner_coefficient')
            
            if not isinstance(outer_coeff, (int, float)) or outer_coeff <= 0:
                raise ValueError(f"设备 {device_code} 的外圈系数必须是正数")
            
            if not isinstance(inner_coeff, (int, float)) or inner_coeff <= 0:
                raise ValueError(f"设备 {device_code} 的内圈系数必须是正数")
        
        return True
    
    def _should_reload(self) -> bool:
        """
        检查是否需要重新加载配置文件
        
        Returns:
            bool: 是否需要重新加载
        """
        if self._config_cache is None:
            return True
        
        try:
            current_modified = self.config_path.stat().st_mtime
            return current_modified != self._last_modified
        except OSError:
            return True
    
    def load_config(self, force_reload: bool = False) -> Dict[str, Any]:
        """
        加载设备配置
        
        Args:
            force_reload (bool): 是否强制重新加载
            
        Returns:
            dict: 设备配置数据
        """
        if force_reload or self._should_reload():
            try:
                config = self._load_yaml_file()
                self._validate_config(config)
                
                self._config_cache = config
                self._last_modified = self.config_path.stat().st_mtime
                
                logger.info(f"成功加载设备配置: {len(config.get('devices', {}))} 个设备")
                
            except Exception as e:
                logger.error(f"加载设备配置失败: {str(e)}")
                
                # 如果有缓存，使用缓存
                if self._config_cache is not None:
                    logger.warning("使用缓存的配置数据")
                    return self._config_cache
                
                # 返回空配置
                logger.warning("返回空配置")
                return {'devices': {}, 'defaults': {}}
        
        return self._config_cache
    
    def get_devices(self, force_reload: bool = False) -> Dict[str, Dict[str, float]]:
        """
        获取设备配置，返回兼容原格式的数据
        
        Args:
            force_reload (bool): 是否强制重新加载
            
        Returns:
            dict: 设备配置字典 {设备编码: [外圈系数, 内圈系数]}
        """
        config = self.load_config(force_reload)
        devices = config.get('devices', {})
        
        # 转换为原格式
        result = {}
        for device_code, device_config in devices.items():
            result[device_code] = [
                device_config['outer_coefficient'],
                device_config['inner_coefficient']
            ]
        
        return result
    
    def get_device_details(self, force_reload: bool = False) -> Dict[str, Dict[str, Any]]:
        """
        获取设备详细配置信息
        
        Args:
            force_reload (bool): 是否强制重新加载
            
        Returns:
            dict: 设备详细配置
        """
        config = self.load_config(force_reload)
        return config.get('devices', {})
    
    def get_device_info(self, device_code: str, force_reload: bool = False) -> Optional[Dict[str, Any]]:
        """
        获取单个设备的配置信息
        
        Args:
            device_code (str): 设备编码
            force_reload (bool): 是否强制重新加载
            
        Returns:
            dict or None: 设备配置信息
        """
        devices = self.get_device_details(force_reload)
        return devices.get(device_code)
    
    def reload_config(self) -> bool:
        """
        重新加载配置文件
        
        Returns:
            bool: 重新加载是否成功
        """
        try:
            self.load_config(force_reload=True)
            return True
        except Exception as e:
            logger.error(f"重新加载配置失败: {str(e)}")
            return False
    
    def get_config_info(self) -> Dict[str, Any]:
        """
        获取配置文件信息
        
        Returns:
            dict: 配置文件信息
        """
        config = self.load_config()
        
        return {
            "config_path": str(self.config_path),
            "exists": self.config_path.exists(),
            "last_modified": datetime.fromtimestamp(self._last_modified).isoformat() if self._last_modified else None,
            "version": config.get('version', 'unknown'),
            "device_count": len(config.get('devices', {})),
            "last_updated": config.get('last_updated', 'unknown')
        }


# 全局配置加载器实例
_device_config_loader = None


def get_device_config_loader() -> DeviceConfigLoader:
    """
    获取全局设备配置加载器实例
    
    Returns:
        DeviceConfigLoader: 配置加载器实例
    """
    global _device_config_loader
    if _device_config_loader is None:
        _device_config_loader = DeviceConfigLoader()
    return _device_config_loader


def load_predefined_devices(force_reload: bool = False) -> Dict[str, list]:
    """
    加载预定义设备配置（兼容原接口）
    
    Args:
        force_reload (bool): 是否强制重新加载
        
    Returns:
        dict: 设备配置字典
    """
    loader = get_device_config_loader()
    return loader.get_devices(force_reload)


def reload_device_config() -> bool:
    """
    重新加载设备配置
    
    Returns:
        bool: 重新加载是否成功
    """
    loader = get_device_config_loader()
    return loader.reload_config()
