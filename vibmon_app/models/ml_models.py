#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习模型管理模块
"""

import os
import pickle
import numpy as np
import warnings
from flask import current_app
from vibmon_app.utils.exceptions import ModelNotLoadedError, PredictionError
from vibmon_app.utils.helpers import safe_float_conversion

# 抑制sklearn版本兼容性警告
warnings.filterwarnings('ignore', category=UserWarning, module='sklearn')


class ModelManager:
    """机器学习模型管理器"""

    def __init__(self):
        self.svm_model = None
        self._app = None
        self._model_path = None
        self._model_loaded_time = None
    
    def init_app(self, app):
        """初始化应用"""
        self._app = app
        with app.app_context():
            self.load_svm_model()

    def cleanup(self):
        """清理资源"""
        self.svm_model = None
        self._model_path = None
        self._model_loaded_time = None
    
    def load_svm_model(self):
        """加载SVM模型"""
        try:
            # 检查是否在应用上下文中
            if not current_app:
                print("[WARNING] 不在Flask应用上下文中，无法加载模型")
                return False

            model_path = current_app.config['MODEL_PATH']

            if not os.path.exists(model_path):
                current_app.logger.error(f"[ERROR] 模型文件不存在: {model_path}")
                return False

            # 临时抑制sklearn版本警告
            with warnings.catch_warnings():
                warnings.filterwarnings('ignore', category=UserWarning, message='.*version.*')
                warnings.filterwarnings('ignore', category=UserWarning, message='.*InconsistentVersionWarning.*')

                with open(model_path, 'rb') as f:
                    self.svm_model = pickle.load(f)

            current_app.logger.info(f"[SUCCESS] 成功加载SVM模型: {model_path}")
            return True

        except Exception as e:
            if current_app:
                current_app.logger.error(f"[ERROR] 模型加载失败: {e}", exc_info=True)
            else:
                print(f"[ERROR] 模型加载失败: {e}")
            self.svm_model = None
            return False
    
    def is_model_loaded(self):
        """检查模型是否已加载"""
        return self.svm_model is not None
    
    def predict_with_svm(self, feature_vector):
        """
        使用SVM模型进行预测

        Args:
            feature_vector (array-like): 特征向量

        Returns:
            float: 预测结果

        Raises:
            ModelNotLoadedError: 模型未加载
            PredictionError: 预测失败
        """
        if not self.is_model_loaded():
            # 尝试重新加载模型
            if current_app:
                current_app.logger.warning("[WARNING] SVM模型未加载，尝试重新加载...")
                if not self.load_svm_model():
                    raise ModelNotLoadedError("SVM模型加载失败，无法进行预测")
            else:
                raise ModelNotLoadedError("SVM模型未加载且无法重新加载")
        
        try:
            # 输入验证和转换
            if isinstance(feature_vector, list):
                feature_vector = np.array(feature_vector)
            elif not isinstance(feature_vector, np.ndarray):
                feature_vector = np.asarray(feature_vector)
            
            # 检查输入是否为空
            if feature_vector.size == 0:
                raise PredictionError("输入特征向量为空")

            # 检查特征向量中的异常值
            if np.any(np.isnan(feature_vector)) or np.any(np.isinf(feature_vector)):
                raise PredictionError("特征向量包含NaN或无穷大值")

            # 确保输入是二维数组
            if feature_vector.ndim == 1:
                feature_vector = feature_vector.reshape(1, -1)
            elif feature_vector.ndim > 2:
                raise PredictionError(f"输入维度错误: {feature_vector.ndim}, 期望1或2维")
            
            # 进行预测
            prediction = self.svm_model.predict(feature_vector)
            
            # 如果模型支持概率预测，也计算概率（用于调试）
            if hasattr(self.svm_model, 'predict_proba'):
                try:
                    probability = self.svm_model.predict_proba(feature_vector)
                    current_app.logger.debug(f"预测概率: {probability}")
                except Exception as prob_e:
                    current_app.logger.warning(f"概率预测失败: {str(prob_e)}")
            
            # 返回预测结果的第一个值
            if len(prediction) > 0:
                return safe_float_conversion(prediction[0])
            else:
                return current_app.config['DEFAULT_PREDICTION_RESULT']
                
        except Exception as e:
            current_app.logger.error(f"SVM预测错误: {str(e)} | 输入形状: {feature_vector.shape if hasattr(feature_vector, 'shape') else 'unknown'}", exc_info=True)
            raise PredictionError(f"预测失败: {str(e)}")


# 全局模型管理器实例
model_manager = ModelManager()
