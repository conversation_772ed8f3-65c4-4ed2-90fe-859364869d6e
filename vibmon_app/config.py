#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用配置模块
"""

import os
from pathlib import Path

# 获取项目根目录
basedir = Path(__file__).parent.parent.absolute()


class Config:
    """基础配置类"""

    # Flask基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    MAX_CONTENT_LENGTH = 20 * 1024 * 1024  # 20MB

    # JSON编码配置 - 确保中文字符正确显示
    JSON_AS_ASCII = False
    JSONIFY_MIMETYPE = 'application/json; charset=utf-8'
    
    # 振动分析配置
    DEFAULT_SAMPLING_RATE = 12000
    MAX_SAMPLES = 12000
    MIN_SAMPLES = 100
    DEFAULT_PREDICTION_RESULT = None
    
    # 必需的CSV列名
    REQUIRED_CSV_COLUMNS = ['horizontal_vibration', 'axial_vibration', 'vertical_vibration']
    
    # 模型配置
    MODEL_PATH = basedir / 'predictive_model' / 'svm_anomaly_detection_model.pkl'
    
    # 数据目录配置
    CSV_DATA_DIR = basedir / 'csv_data'
    UPLOAD_FOLDER = basedir / 'uploads'
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOGLEVEL', 'INFO').upper()
    LOG_DIR = basedir / 'logs'
    
    # NumPy警告抑制
    SUPPRESS_NUMPY_WARNINGS = os.environ.get('SUPPRESS_NUMPY_WARNINGS', 'true').lower() == 'true'

    # 设备配置 - 从YAML文件动态加载
    def _load_predefined_devices(self):
        """动态加载设备配置"""
        try:
            from vibmon_app.utils.config_loader import load_predefined_devices
            return load_predefined_devices()
        except Exception as e:
            # 如果加载失败，返回默认配置
            import logging
            logging.getLogger(__name__).error(f"加载设备配置失败，使用默认配置: {str(e)}")
            return {
                "CAAQ000000497": [5.766, 8.234],  # 1# 机舱风机转速（外圈，内圈） 型号：N319
                "CAAQ000000498": [5.766, 8.234],  # 2# 机舱风机转速
                "CAAQ000000499": [5.766, 8.234],  # 3# 机舱风机转速
                "CAAQ000000500": [5.766, 8.234],  # 4# 机舱风机转速
                "CAAQ000000501": [3.096, 4.940],  # 1# 锅炉强力风机转速 型号：6319C3
                "CAAQ000000502": [3.096, 4.940],  # 2# 锅炉强力风机转速
                "CAAQ000000494": [3.076, 4.924],  # 1# 海水泵转速 型号：6314C3
                "CAAQ000000495": [3.076, 4.924],  # 2# 海水泵转速
                "CAAQ000000496": [3.076, 4.924],  # 3# 海水泵转速
                "CAAQ000000503": [3.037, 4.963]  # 舵机舱风机转速 型号：6309zz
            }

    # 初始化时加载设备配置
    PREDEFINED_DEVICES = None
    
    @staticmethod
    def init_app(app):
        """初始化应用配置"""
        # 创建必要的目录
        for directory in [Config.UPLOAD_FOLDER, Config.LOG_DIR]:
            directory.mkdir(exist_ok=True)

        # 初始化设备配置
        if Config.PREDEFINED_DEVICES is None:
            Config.PREDEFINED_DEVICES = Config._load_predefined_devices(Config)

    @staticmethod
    def reload_device_config():
        """重新加载设备配置"""
        Config.PREDEFINED_DEVICES = Config._load_predefined_devices(Config)
        return Config.PREDEFINED_DEVICES


class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    TESTING = False


class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    TESTING = False


class TestingConfig(Config):
    """测试环境配置"""
    DEBUG = True
    TESTING = True
    WTF_CSRF_ENABLED = False


# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
