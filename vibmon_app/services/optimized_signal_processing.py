#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的信号处理服务模块
针对高频调用场景优化，提升性能
"""

import numpy as np
from scipy import fftpack
from flask import current_app

class OptimizedSignalProcessor:
    """优化的信号处理器"""
    
    def __init__(self):
        """
        初始化信号处理器
        """
        pass
    
    @staticmethod
    def calculate_envelope_spectrum_optimized(signal, sampling_rate):
        """
        Args:
            signal (array-like): 输入信号
            sampling_rate (float): 采样率
            
        Returns:
            tuple: (频率数组, 幅值数组)
        """
        signal = np.asarray(signal, dtype=np.float64)
        
        # 计算希尔伯特变换
        ht = fftpack.hilbert(signal)
        
        # 计算包络信号（向量化操作）
        at = np.sqrt(np.square(ht) + np.square(signal))
        
        # 去直流分量
        at -= np.mean(at)

        # 传统复数FFT
        am = np.fft.fft(at)[:1500]
        am = np.abs(am)
        am = am / len(signal) * 2  # 归一化

        # 计算频率轴
        freq = np.fft.fftfreq(len(at), d=1 / sampling_rate)[:1500]

        
        return freq, am
    

    
    def envelope_spectrum_all_optimized(self, data_h, data_a, data_v, sampling_rate):
        """
        优化的三轴合成信号包络谱计算

        Args:
            data_h, data_a, data_v: 三轴振动数据
            sampling_rate: 采样率

        Returns:
            tuple: (频率列表, 幅值列表)
        """
        # 合成三轴信号（向量化操作）
        combined_signal = np.asarray(data_h) + np.asarray(data_a) + np.asarray(data_v)

        # 使用优化的包络谱计算
        freq, am = self.calculate_envelope_spectrum_optimized(
            combined_signal, sampling_rate)

        # 转换为列表
        freq, am = freq.tolist(), am.tolist()

        return freq, am
    
    def envelope_spectrum_single_optimized(self, data, sampling_rate):
        """
        优化的单轴信号包络谱计算

        Args:
            data: 单轴振动数据
            sampling_rate: 采样率

        Returns:
            tuple: (频率列表, 幅值列表)
        """
        # 使用优化的包络谱计算
        freq, am = self.calculate_envelope_spectrum_optimized(data, sampling_rate)

        # 转换为列表
        freq, am = freq.tolist(), am.tolist()

        return freq, am
    

    
    def __del__(self):
        """清理线程池"""
        if hasattr(self, 'thread_pool'):
            self.thread_pool.shutdown(wait=False)
