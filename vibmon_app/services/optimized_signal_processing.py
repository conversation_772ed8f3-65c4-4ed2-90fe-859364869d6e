#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的信号处理服务模块
针对高频调用场景优化，提升性能
"""

import numpy as np
from scipy import fftpack
from flask import current_app

class OptimizedSignalProcessor:
    """优化的信号处理器"""
    
    def __init__(self):
        """
        初始化信号处理器
        """
        pass
    
    @staticmethod
    def calculate_envelope_spectrum_optimized(signal, sampling_rate):
        """
        计算单个信号的包络谱（优化版本）

        使用向量化操作和实数FFT提升性能，适用于高频调用场景。

        Args:
            signal (array-like): 输入信号数据
            sampling_rate (float): 采样率 (Hz)

        Returns:
            tuple: (频率数组, 幅值数组)
        """
        signal = np.asarray(signal, dtype=np.float64)
        
        # 计算希尔伯特变换
        ht = fftpack.hilbert(signal)
        
        # 计算包络信号（向量化操作）
        at = np.sqrt(np.square(ht) + np.square(signal))
        
        # 去直流分量
        at -= np.mean(at)

        # 使用FFT计算频谱（限制到1500个点以提升性能）
        am = np.fft.fft(at)[:1500]
        am = np.abs(am)
        am = am / len(signal) * 2  # 归一化处理

        # 计算对应的频率轴（只取前1500个点）
        freq = np.fft.fftfreq(len(at), d=1 / sampling_rate)[:1500]

        
        return freq, am
    

    
    def envelope_spectrum_all_optimized(self, data_h, data_a, data_v, sampling_rate):
        """
        优化的三轴合成信号包络谱计算

        将三轴振动数据合成后计算包络谱，使用向量化操作提升性能。

        Args:
            data_h (array-like): 水平方向振动数据
            data_a (array-like): 轴向振动数据
            data_v (array-like): 垂直方向振动数据
            sampling_rate (float): 采样率 (Hz)

        Returns:
            tuple: (频率列表, 幅值列表)
        """
        # 合成三轴信号（向量化操作）
        combined_signal = np.asarray(data_h) + np.asarray(data_a) + np.asarray(data_v)

        # 使用优化的包络谱计算
        freq, am = self.calculate_envelope_spectrum_optimized(
            combined_signal, sampling_rate)

        # 转换为列表
        freq, am = freq.tolist(), am.tolist()

        return freq, am
    
    def envelope_spectrum_single_optimized(self, data, sampling_rate):
        """
        优化的单轴信号包络谱计算

        对单轴振动数据进行包络谱分析，使用优化算法提升计算效率。

        Args:
            data (array-like): 单轴振动数据
            sampling_rate (float): 采样率 (Hz)

        Returns:
            tuple: (频率列表, 幅值列表) - 转换为Python列表便于JSON序列化
        """
        # 使用优化的包络谱计算
        freq, am = self.calculate_envelope_spectrum_optimized(data, sampling_rate)

        # 转换为列表
        freq, am = freq.tolist(), am.tolist()

        return freq, am
    

    
    def __del__(self):
        """清理线程池"""
        if hasattr(self, 'thread_pool'):
            self.thread_pool.shutdown(wait=False)
