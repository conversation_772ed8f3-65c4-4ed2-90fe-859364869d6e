#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
信号处理服务模块
"""

import numpy as np
from scipy import fftpack
from vibmon_app.utils.helpers import safe_float_conversion


class SignalProcessor:
    """信号处理器"""
    
    @staticmethod
    def calculate_envelope_spectrum(signal, sampling_rate):
        """
        计算单个信号的包络谱核心算法
        
        Args:
            signal (array-like): 输入信号
            sampling_rate (float): 采样率
            
        Returns:
            tuple: (频率数组, 幅值数组)
        """
        signal = np.asarray(signal)
        
        # 计算希尔伯特变换
        ht = fftpack.hilbert(signal)
        # 计算包络信号
        at = np.sqrt(ht ** 2 + signal ** 2)
        
        # 去直流分量
        at = at - np.mean(at)
        
        # 快速傅里叶变换
        am = np.fft.fft(at)  # 对包络信号做FFT变换
        am = np.abs(am)  # 获取幅值

        # 安全的归一化处理
        if len(am) > 0:
            am = am / len(am) * 2  # 归一化
        else:
            raise ValueError("信号长度为零，无法进行FFT变换")

        am = am[0: int(len(am) / 2)]  # 只取正频率部分
        
        # 计算频率轴
        freq = np.fft.fftfreq(len(at), d=1 / sampling_rate)  # 获取所有频率
        freq = freq[0:int(len(freq) / 2)]  # 只取正频率
        
        return freq, am
    
    @classmethod
    def envelope_spectrum_all(cls, data_h, data_a, data_v, sampling_rate):
        """
        计算三轴合成信号的包络谱，返回频率和幅值
        
        Args:
            data_h (array-like): 水平方向振动数据
            data_a (array-like): 轴向振动数据
            data_v (array-like): 垂直方向振动数据
            sampling_rate (float): 采样率
            
        Returns:
            tuple: (频率列表, 幅值列表)
        """
        # 合成三轴信号
        combined_signal = np.asarray(data_h) + np.asarray(data_a) + np.asarray(data_v)
        
        # 使用统一的包络谱计算函数
        freq, am = cls.calculate_envelope_spectrum(combined_signal, sampling_rate)
        
        # 转换为Python列表以便JSON序列化
        return freq.tolist(), am.tolist()
    
    @classmethod
    def envelope_spectrum_single(cls, data, sampling_rate):
        """
        计算单轴信号的包络谱，返回频率和幅值
        
        Args:
            data (array-like): 单轴振动数据
            sampling_rate (float): 采样率
            
        Returns:
            tuple: (频率列表, 幅值列表)
        """
        # 使用统一的包络谱计算函数
        freq, am = cls.calculate_envelope_spectrum(data, sampling_rate)
        
        # 转换为Python列表以便JSON序列化
        return freq.tolist(), am.tolist()
    
    @staticmethod
    def combine_signals(data_h, data_a, data_v):
        """
        合成三轴信号
        
        Args:
            data_h (array-like): 水平方向振动数据
            data_a (array-like): 轴向振动数据
            data_v (array-like): 垂直方向振动数据
            
        Returns:
            np.ndarray: 合成信号
        """
        return np.asarray(data_h) + np.asarray(data_a) + np.asarray(data_v)
    
    @staticmethod
    def calculate_combined_rms(data_h, data_a, data_v):
        """
        计算每列的联合RMS值
        
        Args:
            data_h (array-like): 水平方向振动数据
            data_a (array-like): 轴向振动数据
            data_v (array-like): 垂直方向振动数据
            
        Returns:
            np.ndarray: 联合RMS值数组
        """
        data_h = np.asarray(data_h)
        data_a = np.asarray(data_a)
        data_v = np.asarray(data_v)
        
        return np.sqrt((data_h ** 2 + data_a ** 2 + data_v ** 2) / 3)
