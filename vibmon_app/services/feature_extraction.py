#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征提取服务模块
"""

import numpy as np
from scipy.stats import kurtosis
from vibmon_app.utils.helpers import safe_float_conversion
from vibmon_app.services.signal_processing import SignalProcessor


class FeatureExtractor:
    """特征提取器"""
    
    @staticmethod
    def extract_signal_features(data_h, data_a, data_v):
        """
        提取信号的各种特征，包括RMS、峰峰值、均值、峭度等
        
        Args:
            data_h (array-like): 水平方向振动数据
            data_a (array-like): 轴向振动数据
            data_v (array-like): 垂直方向振动数据
            
        Returns:
            dict: 包含各种特征的字典
        """
        # 转换为numpy数组确保数据类型一致
        data_h = np.asarray(data_h)
        data_a = np.asarray(data_a)
        data_v = np.asarray(data_v)
        total_signal = data_h + data_a + data_v
        
        # 计算各轴的有效值(RMS)
        rms_h = np.sqrt(np.mean(data_h ** 2))
        rms_a = np.sqrt(np.mean(data_a ** 2))
        rms_v = np.sqrt(np.mean(data_v ** 2))
        rms_total = np.sqrt(np.mean(total_signal ** 2))
        
        # 计算各轴的峰峰值
        p2p_h = np.ptp(data_h)
        p2p_a = np.ptp(data_a)
        p2p_v = np.ptp(data_v)
        p2p_total = np.ptp(total_signal)
        
        # 计算各轴的均值
        mean_h = np.mean(data_h)
        mean_a = np.mean(data_a)
        mean_v = np.mean(data_v)
        mean_total = np.mean(total_signal)
        
        # 使用scipy.stats.kurtosis计算峭度
        # fisher=False返回原始峭度，fisher=True返回超峭度（减去3）
        kurtosis_h = kurtosis(data_h, fisher=False)
        kurtosis_a = kurtosis(data_a, fisher=False)
        kurtosis_v = kurtosis(data_v, fisher=False)
        
        # 计算每列的联合RMS值
        combined_rms = SignalProcessor.calculate_combined_rms(data_h, data_a, data_v)
        # 计算总幅值total_Amplitude
        total_amplitude = np.sqrt(np.mean(combined_rms ** 2))
        
        # 加合信号的峭度
        total_kurtosis = kurtosis(total_signal, fisher=False)
        
        # 返回所有计算出的特征（使用安全转换避免NumPy 1.25+警告）
        return {
            'rms': {
                'h': safe_float_conversion(rms_h),
                'a': safe_float_conversion(rms_a),
                'v': safe_float_conversion(rms_v),
                'total': safe_float_conversion(rms_total)
            },
            'p2p': {
                'h': safe_float_conversion(p2p_h),
                'a': safe_float_conversion(p2p_a),
                'v': safe_float_conversion(p2p_v),
                'total': safe_float_conversion(p2p_total)
            },
            'mean': {
                'h': safe_float_conversion(mean_h),
                'a': safe_float_conversion(mean_a),
                'v': safe_float_conversion(mean_v),
                'total': safe_float_conversion(mean_total)
            },
            'kurtosis': {
                'h': safe_float_conversion(kurtosis_h),
                'a': safe_float_conversion(kurtosis_a),
                'v': safe_float_conversion(kurtosis_v),
            },
            'total_amplitude': safe_float_conversion(total_amplitude),
            'total_kurtosis': safe_float_conversion(total_kurtosis)
        }
    
    @staticmethod
    def calculate_rms(data):
        """
        计算RMS值
        
        Args:
            data (array-like): 输入数据
            
        Returns:
            float: RMS值
        """
        data = np.asarray(data)
        return safe_float_conversion(np.sqrt(np.mean(data ** 2)))
    
    @staticmethod
    def calculate_peak_to_peak(data):
        """
        计算峰峰值
        
        Args:
            data (array-like): 输入数据
            
        Returns:
            float: 峰峰值
        """
        data = np.asarray(data)
        return safe_float_conversion(np.ptp(data))
    
    @staticmethod
    def calculate_kurtosis(data, fisher=False):
        """
        计算峭度
        
        Args:
            data (array-like): 输入数据
            fisher (bool): 是否返回超峭度（减去3）
            
        Returns:
            float: 峭度值
        """
        data = np.asarray(data)
        return safe_float_conversion(kurtosis(data, fisher=fisher))
