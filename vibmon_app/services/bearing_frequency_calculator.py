#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轴承特征频率计算服务
用于计算轴承内圈和外圈的推荐特征频率
"""

from flask import current_app
from vibmon_app.utils.exceptions import InvalidDataError


class BearingFrequencyCalculator:
    """轴承特征频率计算器"""
    
    @staticmethod
    def calculate_characteristic_frequencies(equipment_code: str, rpm: float) -> dict:
        """
        计算轴承特征频率
        
        Args:
            equipment_code (str): 设备编码
            rpm (float): 转速 (转/分钟)
            
        Returns:
            dict: 包含内圈和外圈特征频率的字典
            {
                'outer_fcf': float,  # 外圈特征频率 (Hz)
                'inner_fcf': float,  # 内圈特征频率 (Hz)
                'rotation_frequency': float,  # 转频 (Hz)
                'device_coefficients': list  # 设备系数 [外圈, 内圈]
            }
            
        Raises:
            InvalidDataError: 设备不存在或参数无效时抛出
        """
        # 参数验证
        if not isinstance(equipment_code, str) or not equipment_code.strip():
            raise InvalidDataError("设备编码不能为空")
            
        if not isinstance(rpm, (int, float)) or rpm <= 0:
            raise InvalidDataError("转速必须为正数")
            
        # 获取设备配置
        predefined_devices = current_app.config.get('PREDEFINED_DEVICES')
        if predefined_devices is None:
            # 如果配置为None，尝试重新加载
            from vibmon_app.config import Config
            predefined_devices = Config.reload_device_config()

        if not predefined_devices or equipment_code not in predefined_devices:
            # 提供可用设备列表
            available_devices = list(predefined_devices.keys())
            raise InvalidDataError(
                f"未知设备编码: {equipment_code}。"
                f"可用设备: {', '.join(available_devices)}"
            )
        
        # 获取设备系数
        device_coefficients = predefined_devices[equipment_code]
        outer_coefficient, inner_coefficient = device_coefficients
        
        # 计算转频 (Hz)
        rotation_frequency = rpm / 60.0
        
        # 计算特征频率
        outer_fcf = rotation_frequency * outer_coefficient
        inner_fcf = rotation_frequency * inner_coefficient
        
        return {
            'outer_fcf': round(outer_fcf, 2),
            'inner_fcf': round(inner_fcf, 2),
            'rotation_frequency': round(rotation_frequency, 2),
            'equipment_code': equipment_code
        }
    
    @staticmethod
    def get_available_devices() -> dict:
        """
        获取所有可用设备及其配置

        Returns:
            dict: 设备配置字典
        """
        devices = current_app.config.get('PREDEFINED_DEVICES')
        if devices is None:
            # 如果配置为None，尝试重新加载
            from vibmon_app.config import Config
            devices = Config.reload_device_config()
        return devices or {}
    
    @staticmethod
    def add_device_configuration(equipment_code: str, outer_coeff: float, inner_coeff: float) -> bool:
        """
        添加新的设备配置（运行时添加，不持久化）
        
        Args:
            equipment_code (str): 设备编码
            outer_coeff (float): 外圈系数
            inner_coeff (float): 内圈系数
            
        Returns:
            bool: 添加是否成功
        """
        try:
            if not hasattr(current_app.config, 'PREDEFINED_DEVICES'):
                current_app.config['PREDEFINED_DEVICES'] = {}
            
            current_app.config['PREDEFINED_DEVICES'][equipment_code] = [outer_coeff, inner_coeff]
            
            current_app.logger.info(
                f"添加设备配置: {equipment_code} -> [{outer_coeff}, {inner_coeff}]"
            )
            return True
            
        except Exception as e:
            current_app.logger.error(f"添加设备配置失败: {e}")
            return False
    
    @staticmethod
    def validate_device_coefficients(outer_coeff: float, inner_coeff: float) -> bool:
        """
        验证设备系数的合理性
        
        Args:
            outer_coeff (float): 外圈系数
            inner_coeff (float): 内圈系数
            
        Returns:
            bool: 系数是否合理
        """
        # 一般轴承的系数范围
        if not (1.0 <= outer_coeff <= 10.0):
            return False
            
        if not (3.0 <= inner_coeff <= 15.0):
            return False
            
        # 内圈系数通常大于外圈系数
        if inner_coeff <= outer_coeff:
            return False
            
        return True
    
    @staticmethod
    def get_frequency_analysis_summary(equipment_code: str, rpm: float) -> dict:
        """
        获取频率分析摘要信息
        
        Args:
            equipment_code (str): 设备编码
            rpm (float): 转速
            
        Returns:
            dict: 频率分析摘要
        """
        try:
            freq_data = BearingFrequencyCalculator.calculate_characteristic_frequencies(
                equipment_code, rpm
            )
            
            return {
                'equipment_info': {
                    'code': equipment_code,
                    'rpm': rpm,
                    'rotation_frequency_hz': freq_data['rotation_frequency']
                },
                'bearing_frequencies': {
                    'outer_ring_fcf_hz': freq_data['outer_fcf'],
                    'inner_ring_fcf_hz': freq_data['inner_fcf'],
                    'coefficients': {
                        'outer_coefficient': freq_data['device_coefficients'][0],
                        'inner_coefficient': freq_data['device_coefficients'][1]
                    }
                },
                'analysis_notes': [
                    f"转频: {freq_data['rotation_frequency']} Hz",
                    f"外圈特征频率: {freq_data['outer_fcf']} Hz",
                    f"内圈特征频率: {freq_data['inner_fcf']} Hz",
                    "建议重点关注这些频率附近的幅值变化"
                ]
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'equipment_code': equipment_code,
                'rpm': rpm
            }
