#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健康评估服务模块
"""

import numpy as np
from scipy.stats import kurtosis
from flask import current_app
from vibmon_app.utils.helpers import safe_float_conversion


class HealthAssessor:
    """健康评估器"""
    
    @staticmethod
    def assess_health(data_h, data_a, data_v):
        """
        基于振动信号峭度的设备健康度评估
        
        Args:
            data_h (array-like): 水平方向振动数据
            data_a (array-like): 轴向振动数据
            data_v (array-like): 垂直方向振动数据
            
        Returns:
            float: 健康度百分比 (0-100)
        """
        # 转换为numpy数组
        data_h = np.asarray(data_h)
        data_a = np.asarray(data_a)
        data_v = np.asarray(data_v)
        
        # 复合信号的峭度计算
        signal = data_h + data_a + data_v
        signal_kurtosis = kurtosis(signal, fisher=False)
        
        # 输入验证
        if signal_kurtosis <= 0:
            current_app.logger.warning(f"峭度值异常: {signal_kurtosis}, 返回最低健康度")
            return 0.0
        
        # kurtosis <=3 时，健康度为100%
        if signal_kurtosis <= 3:
            return 100.0
        
        # 3 < kurtosis <=10 时，使用对数函数计算健康度
        health = -81.96 * np.log(signal_kurtosis) + 191.94
        # 确保健康度在0-100%范围内，并安全转换为Python float
        health_degree = safe_float_conversion(max(0.0, min(100.0, health)))
        
        return health_degree
    
    @staticmethod
    def adjust_health_for_warnings(health_degree, early_warning_status, alarm_status):
        """
        根据预警和报警状态调整健康度
        
        Args:
            health_degree (float): 原始健康度
            early_warning_status (int): 预警状态
            alarm_status (int): 报警状态
            
        Returns:
            float: 调整后的健康度
        """
        if health_degree is None:
            return None
        
        # 只存在预警判断
        if early_warning_status > 0 and alarm_status == 0:
            if health_degree > 70:
                health_degree = 70 - 2 * early_warning_status
            else:
                health_degree = health_degree - 2 * early_warning_status
        
        # 存在报警判断
        if alarm_status > 0:
            if health_degree > 50:
                health_degree = 50 - 2 * alarm_status
            else:
                health_degree = health_degree - 2 * alarm_status
        
        # 确保健康度不为负数
        if health_degree < 0:
            health_degree = 0
        
        return health_degree
    
    @staticmethod
    def get_health_status(health_degree):
        """
        根据健康度获取健康状态描述
        
        Args:
            health_degree (float): 健康度百分比
            
        Returns:
            dict: 包含状态码和描述的字典
        """
        if health_degree is None:
            return {"status": "unknown", "description": "健康度未计算"}
        
        if health_degree >= 90:
            return {"status": "excellent", "description": "设备状态优秀"}
        elif health_degree >= 70:
            return {"status": "good", "description": "设备状态良好"}
        elif health_degree >= 50:
            return {"status": "warning", "description": "设备需要关注"}
        elif health_degree >= 30:
            return {"status": "alarm", "description": "设备状态异常"}
        else:
            return {"status": "critical", "description": "设备状态严重异常"}
    
    @classmethod
    def comprehensive_health_assessment(cls, data_h, data_a, data_v, 
                                       early_warning_status=0, alarm_status=0):
        """
        综合健康评估
        
        Args:
            data_h (array-like): 水平方向振动数据
            data_a (array-like): 轴向振动数据
            data_v (array-like): 垂直方向振动数据
            early_warning_status (int): 预警状态
            alarm_status (int): 报警状态
            
        Returns:
            dict: 包含健康度、状态等信息的字典
        """
        # 计算基础健康度
        base_health = cls.assess_health(data_h, data_a, data_v)
        
        # 根据预警和报警状态调整健康度
        adjusted_health = cls.adjust_health_for_warnings(
            base_health, early_warning_status, alarm_status
        )
        
        # 获取健康状态描述
        health_status = cls.get_health_status(adjusted_health)
        
        return {
            "base_health_degree": base_health,
            "adjusted_health_degree": adjusted_health,
            "health_status": health_status,
            "early_warning_status": early_warning_status,
            "alarm_status": alarm_status
        }
