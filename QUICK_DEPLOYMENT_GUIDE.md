# 振动监测系统 Docker 快速部署指南

**版本**: v2.2.0 Final | **更新日期**: 2025-07-31

## 🚀 两种部署方式

### 方式1: 自动部署 (推荐)

#### 使用一键部署脚本
```bash
# 给脚本执行权限
chmod +x deploy.sh

# 运行部署脚本
./deploy.sh
```

脚本会自动：
- 检查Docker环境
- 引导您配置必需参数
- 自动替换配置文件
- 启动服务并验证

### 方式2: 手动部署

#### 1. 编辑配置文件
```bash
# 编辑docker-compose.yml
vim docker-compose.yml
```

#### 2. 必须修改的参数
```yaml
# 在 docker-compose.yml 中修改以下4个参数：

services:
  vibmon-app:
    # 1. 修改为您的镜像地址
    image: your-registry/vibmon:latest

    environment:
      # 2. 修改为您的MinIO服务器地址
      - MINIO_ENDPOINT=http://your-minio-server:9000

      # 3. 修改为您的MinIO访问密钥
      - MINIO_ACCESS_KEY=your-minio-access-key

      # 4. 修改为您的MinIO密钥
      - MINIO_SECRET_KEY=your-minio-secret-key
```

### 3. 验证配置 (推荐)

#### 检查配置文件
```bash
# 检查YAML文件语法
python -c "import yaml; yaml.safe_load(open('config/minio.yaml'))"
python -c "import yaml; yaml.safe_load(open('config/devices.yaml'))"
python -c "import yaml; yaml.safe_load(open('docker-compose.yml'))"
```

#### 检查环境变量
```bash
# 确保关键环境变量已设置
grep -E "SECRET_KEY|MINIO_ENDPOINT|MINIO_ACCESS_KEY" .env
```

### 4. 启动服务

#### 启动容器
```bash
# 启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f vibmon-app
```

### 5. 验证部署

#### 健康检查
```bash
# 检查服务状态
curl http://localhost:5000/api/status

# 获取设备列表
curl http://localhost:5000/api/devices

# 检查配置信息
curl http://localhost:5000/api/config/info
```

#### 测试接口
```bash
# 测试freq_am接口
curl -X POST http://localhost:5000/api/features/freq_am \
  -H "Content-Type: application/json" \
  -d '{
    "equipment_code": "CAAQ000000497",
    "minio_url": "http://your-minio/bucket/file.csv",
    "rpm": 1800
  }'
```

## ⚠️ 常见问题和解决方案

### 问题1: 容器启动失败
```bash
# 查看详细错误日志
docker-compose logs vibmon-app

# 常见原因：
# - 环境变量未正确设置
# - MinIO服务器无法连接
# - 配置文件格式错误
```

### 问题2: MinIO连接失败
```bash
# 检查网络连接
docker-compose exec vibmon-app ping your-minio-server

# 检查MinIO配置
docker-compose exec vibmon-app cat /app/config/minio.yaml

# 常见原因：
# - MinIO服务器地址错误
# - 访问密钥错误
# - 网络不通
```

### 问题3: 设备编码错误
```bash
# 检查设备配置
curl http://localhost:5000/api/devices

# 编辑设备配置
vim config/devices.yaml

# 重启服务
docker-compose restart vibmon-app
```

## 📋 部署检查清单

### 部署前检查
- [ ] .env文件已创建并配置
- [ ] docker-compose.yml已配置镜像地址
- [ ] MinIO服务器可访问
- [ ] 存储桶已创建或允许自动创建
- [ ] 配置文件语法正确

### 部署后检查
- [ ] 容器正常启动
- [ ] 健康检查通过
- [ ] API接口响应正常
- [ ] 日志无错误信息
- [ ] MinIO连接正常

## 🔧 高级配置

### 自定义资源限制
```yaml
# 在docker-compose.yml中调整
deploy:
  resources:
    limits:
      memory: 4G      # 根据需要调整
      cpus: '2.0'     # 根据需要调整
```

### 自定义网络配置
```yaml
# 使用外部网络
networks:
  vibmon-network:
    external: true
    name: your-existing-network
```

### 持久化日志
```yaml
# 挂载日志到主机
volumes:
  - /host/path/logs:/app/logs:rw
```

## 🎯 生产环境建议

### 安全配置
- 使用强密码作为SECRET_KEY
- 限制MinIO访问权限
- 配置HTTPS（如果需要）
- 定期更新镜像

### 监控配置
- 配置日志轮转
- 设置资源监控
- 配置健康检查告警
- 备份重要配置文件

### 性能优化
- 根据负载调整资源限制
- 优化MinIO网络连接
- 配置适当的日志级别
- 定期清理日志文件

---

**部署完成后，您的振动监测系统将在 http://localhost:5000 上运行！**
