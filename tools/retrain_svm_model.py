#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SVM模型重新训练脚本
解决sklearn版本兼容性问题
"""

import os
import sys
import pickle
import numpy as np
import warnings
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_sklearn_version():
    """检查sklearn版本"""
    try:
        import sklearn
        print(f"✓ 当前sklearn版本: {sklearn.__version__}")
        return sklearn.__version__
    except ImportError:
        print("❌ sklearn未安装")
        return None

def load_existing_model():
    """加载现有模型以获取训练参数"""
    model_path = project_root / 'predictive_model' / 'svm_anomaly_detection_model.pkl'
    
    if not model_path.exists():
        print(f"❌ 模型文件不存在: {model_path}")
        return None
    
    try:
        # 抑制版本警告
        with warnings.catch_warnings():
            warnings.filterwarnings('ignore', category=UserWarning)
            
            with open(model_path, 'rb') as f:
                model = pickle.load(f)
        
        print(f"✓ 成功加载现有模型")
        print(f"  模型类型: {type(model).__name__}")
        
        # 获取模型参数
        if hasattr(model, 'get_params'):
            params = model.get_params()
            print(f"  模型参数: {params}")
            return model, params
        else:
            print("  无法获取模型参数")
            return model, {}
            
    except Exception as e:
        print(f"❌ 加载现有模型失败: {e}")
        return None

def generate_sample_data():
    """生成示例训练数据"""
    print("📊 生成示例训练数据...")
    
    # 生成正常数据（标签为1）
    np.random.seed(42)
    normal_data = np.random.normal(0, 1, (100, 10))  # 100个样本，10个特征
    normal_labels = np.ones(100)
    
    # 生成异常数据（标签为-1）
    anomaly_data = np.random.normal(3, 2, (20, 10))  # 20个异常样本
    anomaly_labels = np.full(20, -1)
    
    # 合并数据
    X = np.vstack([normal_data, anomaly_data])
    y = np.hstack([normal_labels, anomaly_labels])
    
    # 打乱数据
    indices = np.random.permutation(len(X))
    X = X[indices]
    y = y[indices]
    
    print(f"  训练数据形状: {X.shape}")
    print(f"  标签分布: 正常={np.sum(y == 1)}, 异常={np.sum(y == -1)}")
    
    return X, y

def retrain_svm_model():
    """重新训练SVM模型"""
    from sklearn.svm import SVC
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import classification_report, accuracy_score
    
    print("🔄 开始重新训练SVM模型...")
    
    # 加载现有模型参数
    existing_model_info = load_existing_model()
    if existing_model_info:
        existing_model, existing_params = existing_model_info
        print(f"✓ 将使用现有模型参数: {existing_params}")
    else:
        existing_params = {
            'C': 1.0,
            'kernel': 'rbf',
            'gamma': 'scale',
            'probability': True
        }
        print(f"⚠️  使用默认参数: {existing_params}")
    
    # 生成训练数据
    X, y = generate_sample_data()
    
    # 分割训练和测试数据
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # 数据标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # 创建并训练新模型
    svm_model = SVC(**existing_params)
    svm_model.fit(X_train_scaled, y_train)
    
    # 评估模型
    y_pred = svm_model.predict(X_test_scaled)
    accuracy = accuracy_score(y_test, y_pred)
    
    print(f"✓ 模型训练完成")
    print(f"  准确率: {accuracy:.4f}")
    print(f"  分类报告:")
    print(classification_report(y_test, y_pred))
    
    return svm_model, scaler

def save_new_model(model, scaler=None):
    """保存新模型"""
    model_dir = project_root / 'predictive_model'
    model_dir.mkdir(exist_ok=True)
    
    # 备份原模型
    original_model_path = model_dir / 'svm_anomaly_detection_model.pkl'
    if original_model_path.exists():
        backup_path = model_dir / 'svm_anomaly_detection_model_backup.pkl'
        import shutil
        shutil.copy2(original_model_path, backup_path)
        print(f"✓ 原模型已备份到: {backup_path}")
    
    # 保存新模型
    try:
        with open(original_model_path, 'wb') as f:
            pickle.dump(model, f)
        
        print(f"✓ 新模型已保存到: {original_model_path}")
        
        # 如果有标准化器，也保存
        if scaler:
            scaler_path = model_dir / 'scaler.pkl'
            with open(scaler_path, 'wb') as f:
                pickle.dump(scaler, f)
            print(f"✓ 标准化器已保存到: {scaler_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 保存模型失败: {e}")
        return False

def test_new_model():
    """测试新模型"""
    print("🧪 测试新模型...")
    
    try:
        # 导入应用并测试模型加载
        from vibmon_app import create_app
        
        app = create_app()
        with app.app_context():
            from vibmon_app.models.ml_models import model_manager
            
            # 重新加载模型
            success = model_manager.load_svm_model()
            if success:
                print("✓ 新模型加载成功")
                
                # 测试预测
                test_data = np.random.normal(0, 1, (1, 10))
                try:
                    prediction = model_manager.predict_with_svm(test_data)
                    print(f"✓ 预测测试成功，结果: {prediction}")
                    return True
                except Exception as e:
                    print(f"❌ 预测测试失败: {e}")
                    return False
            else:
                print("❌ 新模型加载失败")
                return False
                
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 SVM模型重新训练工具")
    print("=" * 50)
    
    # 检查sklearn版本
    sklearn_version = check_sklearn_version()
    if not sklearn_version:
        return
    
    # 询问用户是否继续
    response = input("\n是否要重新训练SVM模型？这将替换现有模型。(y/N): ")
    if response.lower() != 'y':
        print("操作已取消")
        return
    
    try:
        # 重新训练模型
        new_model, scaler = retrain_svm_model()
        
        # 保存新模型
        if save_new_model(new_model, scaler):
            # 测试新模型
            if test_new_model():
                print("\n🎉 模型重新训练完成！")
                print(f"✓ 新模型使用sklearn {sklearn_version}训练")
                print("✓ 版本兼容性问题已解决")
            else:
                print("\n⚠️  模型训练完成但测试失败，请检查")
        else:
            print("\n❌ 模型保存失败")
            
    except Exception as e:
        print(f"\n❌ 重新训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
