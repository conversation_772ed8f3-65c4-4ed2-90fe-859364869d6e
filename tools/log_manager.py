#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志管理工具
用于日志分析、压缩和清理

使用方法:
    python -m tools.log_manager --analyze  # 分析日志使用情况
    python -m tools.log_manager --compress # 压缩旧日志
    python -m tools.log_manager --clean    # 清理过期日志
"""

import os
import sys
import gzip
import shutil
import argparse
import logging
import datetime
import json
from pathlib import Path
from collections import Counter, defaultdict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class LogManager:
    """日志管理器"""
    
    def __init__(self, log_dir='logs'):
        """
        初始化日志管理器
        
        Args:
            log_dir (str): 日志目录
        """
        self.log_dir = Path(log_dir)
        self.logger = self._setup_logger()
        
        # 确保日志目录存在
        if not self.log_dir.exists():
            self.log_dir.mkdir(parents=True)
            self.logger.info(f"创建日志目录: {self.log_dir}")
    
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger('log_manager')
        logger.setLevel(logging.INFO)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(formatter)
        
        # 添加处理器
        logger.addHandler(console_handler)
        
        return logger
    
    def analyze_logs(self):
        """分析日志使用情况"""
        self.logger.info("开始分析日志使用情况...")
        
        # 获取所有日志文件
        log_files = list(self.log_dir.glob('*.log*'))
        
        if not log_files:
            self.logger.warning(f"在目录 {self.log_dir} 中没有找到日志文件")
            return
        
        # 统计信息
        total_size = 0
        file_count = 0
        file_sizes = {}
        api_calls = Counter()
        error_counts = Counter()
        
        # 分析每个日志文件
        for log_file in log_files:
            file_size = log_file.stat().st_size
            total_size += file_size
            file_count += 1
            file_sizes[str(log_file)] = file_size
            
            # 分析API调用统计
            if 'api_stats.log' in str(log_file):
                self._analyze_api_stats(log_file, api_calls, error_counts)
        
        # 打印统计信息
        self.logger.info(f"日志文件总数: {file_count}")
        self.logger.info(f"日志总大小: {self._format_size(total_size)}")
        
        # 按大小排序文件
        sorted_files = sorted(file_sizes.items(), key=lambda x: x[1], reverse=True)
        self.logger.info("\n最大的5个日志文件:")
        for file_path, size in sorted_files[:5]:
            self.logger.info(f"  {file_path}: {self._format_size(size)}")
        
        # 打印API调用统计
        if api_calls:
            self.logger.info("\nAPI调用统计:")
            for endpoint, count in api_calls.most_common(5):
                self.logger.info(f"  {endpoint}: {count}次调用")
        
        # 打印错误统计
        if error_counts:
            self.logger.info("\n错误统计:")
            for error_type, count in error_counts.most_common(5):
                self.logger.info(f"  {error_type}: {count}次错误")
    
    def _analyze_api_stats(self, log_file, api_calls, error_counts):
        """分析API统计日志"""
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if 'API_CALL' in line:
                        parts = line.split('|')
                        if len(parts) >= 3:
                            endpoint = parts[1]
                            api_calls[endpoint] += 1
                    elif 'API_ERROR' in line:
                        parts = line.split('|')
                        if len(parts) >= 7:
                            error_message = parts[6]
                            error_type = error_message.split(':')[0] if ':' in error_message else error_message
                            error_counts[error_type] += 1
        except Exception as e:
            self.logger.error(f"分析日志文件 {log_file} 失败: {e}")
    
    def compress_old_logs(self, days_threshold=7):
        """
        压缩旧日志文件
        
        Args:
            days_threshold (int): 多少天前的日志被视为旧日志
        """
        self.logger.info(f"开始压缩 {days_threshold} 天前的日志文件...")
        
        # 获取所有日志文件
        log_files = [f for f in self.log_dir.glob('*.log.*') if not str(f).endswith('.gz')]
        
        if not log_files:
            self.logger.info("没有找到需要压缩的日志文件")
            return
        
        # 当前时间
        now = datetime.datetime.now()
        threshold_date = now - datetime.timedelta(days=days_threshold)
        
        # 压缩旧日志
        compressed_count = 0
        for log_file in log_files:
            # 获取文件修改时间
            mtime = datetime.datetime.fromtimestamp(log_file.stat().st_mtime)
            
            # 如果文件比阈值旧，则压缩
            if mtime < threshold_date:
                try:
                    # 压缩文件
                    with open(log_file, 'rb') as f_in:
                        with gzip.open(f"{log_file}.gz", 'wb') as f_out:
                            shutil.copyfileobj(f_in, f_out)
                    
                    # 删除原文件
                    log_file.unlink()
                    compressed_count += 1
                    self.logger.info(f"压缩文件: {log_file}")
                except Exception as e:
                    self.logger.error(f"压缩文件 {log_file} 失败: {e}")
        
        self.logger.info(f"压缩完成，共压缩 {compressed_count} 个文件")
    
    def clean_expired_logs(self, days_threshold=30):
        """
        清理过期的日志文件
        
        Args:
            days_threshold (int): 多少天前的日志被视为过期
        """
        self.logger.info(f"开始清理 {days_threshold} 天前的日志文件...")
        
        # 获取所有压缩日志文件
        log_files = list(self.log_dir.glob('*.log.*.gz'))
        
        if not log_files:
            self.logger.info("没有找到需要清理的日志文件")
            return
        
        # 当前时间
        now = datetime.datetime.now()
        threshold_date = now - datetime.timedelta(days=days_threshold)
        
        # 清理过期日志
        cleaned_count = 0
        for log_file in log_files:
            # 获取文件修改时间
            mtime = datetime.datetime.fromtimestamp(log_file.stat().st_mtime)
            
            # 如果文件比阈值旧，则删除
            if mtime < threshold_date:
                try:
                    log_file.unlink()
                    cleaned_count += 1
                    self.logger.info(f"删除文件: {log_file}")
                except Exception as e:
                    self.logger.error(f"删除文件 {log_file} 失败: {e}")
        
        self.logger.info(f"清理完成，共删除 {cleaned_count} 个文件")
    
    def _format_size(self, size_bytes):
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.2f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.2f} TB"


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='日志管理工具')
    parser.add_argument('--analyze', action='store_true', help='分析日志使用情况')
    parser.add_argument('--compress', action='store_true', help='压缩旧日志')
    parser.add_argument('--clean', action='store_true', help='清理过期日志')
    parser.add_argument('--days', type=int, default=7, help='压缩多少天前的日志')
    parser.add_argument('--expire', type=int, default=30, help='清理多少天前的日志')
    
    args = parser.parse_args()
    
    # 创建日志管理器
    log_manager = LogManager()
    
    # 执行操作
    if args.analyze:
        log_manager.analyze_logs()
    
    if args.compress:
        log_manager.compress_old_logs(args.days)
    
    if args.clean:
        log_manager.clean_expired_logs(args.expire)
    
    # 如果没有指定操作，显示帮助
    if not (args.analyze or args.compress or args.clean):
        parser.print_help()


if __name__ == "__main__":
    main()
