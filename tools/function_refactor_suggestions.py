#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
函数重构建议工具
为过长的函数提供重构建议
"""

import ast
from pathlib import Path
from typing import List, Dict, Any


class FunctionRefactorSuggester:
    """函数重构建议器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.suggestions = []
    
    def analyze_long_functions(self) -> List[Dict[str, Any]]:
        """分析过长的函数并提供重构建议"""
        python_files = list(self.project_root.rglob("*.py"))
        
        for file_path in python_files:
            if any(part in str(file_path) for part in ['venv', '__pycache__', '.git']):
                continue
            
            self.analyze_file(file_path)
        
        return self.suggestions
    
    def analyze_file(self, file_path: Path):
        """分析单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                    self.analyze_function(file_path, node, content)
                    
        except Exception as e:
            print(f"分析文件失败 {file_path}: {e}")
    
    def analyze_function(self, file_path: Path, node: ast.FunctionDef, content: str):
        """分析单个函数"""
        if not hasattr(node, 'end_lineno') or not node.end_lineno:
            return
        
        lines = node.end_lineno - node.lineno
        if lines <= 50:  # 只分析超过50行的函数
            return
        
        # 获取函数内容
        content_lines = content.split('\n')
        func_content = '\n'.join(content_lines[node.lineno-1:node.end_lineno])
        
        # 分析函数复杂度
        suggestions = self.generate_refactor_suggestions(node, func_content, lines)
        
        self.suggestions.append({
            'file': str(file_path.relative_to(self.project_root)),
            'function': node.name,
            'start_line': node.lineno,
            'lines': lines,
            'suggestions': suggestions
        })
    
    def generate_refactor_suggestions(self, node: ast.FunctionDef, content: str, lines: int) -> List[str]:
        """生成重构建议"""
        suggestions = []
        
        # 基于行数的建议
        if lines > 100:
            suggestions.append("🔥 函数过长（>100行），强烈建议拆分为多个小函数")
        elif lines > 50:
            suggestions.append("⚠️ 函数较长（>50行），建议考虑拆分")
        
        # 分析函数内容
        if content.count('try:') > 3:
            suggestions.append("🔧 包含多个try-except块，建议提取错误处理逻辑")
        
        if content.count('if ') > 10:
            suggestions.append("🔧 包含过多条件判断，建议使用策略模式或提取判断逻辑")
        
        if content.count('for ') > 5:
            suggestions.append("🔧 包含多个循环，建议提取循环逻辑为独立函数")
        
        # 检查是否有明显的代码块
        if '# ' in content:
            comment_count = content.count('# ')
            if comment_count > 5:
                suggestions.append("💡 包含多个注释块，可以按注释分组拆分函数")
        
        # 检查变量数量
        var_assignments = content.count(' = ')
        if var_assignments > 20:
            suggestions.append("📝 变量过多，建议使用数据类或字典组织数据")
        
        return suggestions
    
    def print_suggestions(self):
        """打印重构建议"""
        if not self.suggestions:
            print("✅ 没有发现需要重构的长函数！")
            return
        
        print(f"\n📊 函数重构建议报告")
        print("=" * 60)
        print(f"发现 {len(self.suggestions)} 个需要重构的函数\n")
        
        # 按行数排序
        sorted_suggestions = sorted(self.suggestions, key=lambda x: x['lines'], reverse=True)
        
        for i, suggestion in enumerate(sorted_suggestions[:10], 1):  # 只显示前10个
            print(f"{i}. 📁 {suggestion['file']}")
            print(f"   🔧 函数: {suggestion['function']} (第{suggestion['start_line']}行)")
            print(f"   📏 长度: {suggestion['lines']}行")
            print("   💡 建议:")
            for sug in suggestion['suggestions']:
                print(f"      {sug}")
            print()
        
        if len(self.suggestions) > 10:
            print(f"... 还有 {len(self.suggestions) - 10} 个函数需要重构")
        
        print("\n🎯 重构优先级建议:")
        print("1. 优先重构超过100行的函数")
        print("2. 重构包含多个职责的函数")
        print("3. 提取可复用的代码块")
        print("4. 使用设计模式简化复杂逻辑")


def main():
    """主函数"""
    project_root = Path(__file__).parent.parent
    
    suggester = FunctionRefactorSuggester(str(project_root))
    suggester.analyze_long_functions()
    suggester.print_suggestions()


if __name__ == '__main__':
    main()
