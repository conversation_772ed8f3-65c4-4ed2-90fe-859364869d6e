#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码质量检查工具
检查项目中的常见代码问题
"""

import os
import ast
import sys
from pathlib import Path
from typing import List, Dict, Any


class CodeQualityChecker:
    """代码质量检查器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.issues = []
    
    def check_all(self) -> Dict[str, Any]:
        """执行所有检查"""
        print("🔍 开始代码质量检查...")
        
        # 获取所有Python文件
        python_files = list(self.project_root.rglob("*.py"))
        
        for file_path in python_files:
            # 跳过虚拟环境和缓存目录
            if any(part in str(file_path) for part in ['venv', '__pycache__', '.git']):
                continue
            
            self.check_file(file_path)
        
        return self.generate_report()
    
    def check_file(self, file_path: Path):
        """检查单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析AST
            try:
                tree = ast.parse(content)
            except SyntaxError as e:
                self.add_issue(file_path, "语法错误", f"第{e.lineno}行: {e.msg}")
                return
            
            # 执行各种检查
            self.check_bare_except(file_path, tree)
            self.check_unused_imports(file_path, content)
            self.check_long_functions(file_path, tree)
            self.check_security_issues(file_path, content)
            
        except Exception as e:
            self.add_issue(file_path, "文件读取错误", str(e))
    
    def check_bare_except(self, file_path: Path, tree: ast.AST):
        """检查裸露的except语句"""
        for node in ast.walk(tree):
            if isinstance(node, ast.ExceptHandler):
                if node.type is None:
                    self.add_issue(
                        file_path, 
                        "裸露except", 
                        f"第{node.lineno}行: 使用了裸露的except语句，应该指定具体的异常类型"
                    )
    
    def check_unused_imports(self, file_path: Path, content: str):
        """检查未使用的导入"""
        lines = content.split('\n')
        imports = []
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if line.startswith('import ') or line.startswith('from '):
                # 简单的未使用导入检查
                if 'import' in line and not any(
                    name in content for name in line.split() 
                    if name not in ['import', 'from', 'as']
                ):
                    # 这是一个简化的检查，实际项目中建议使用专业工具
                    pass
    
    def check_long_functions(self, file_path: Path, tree: ast.AST):
        """检查过长的函数"""
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                # 计算函数行数
                if hasattr(node, 'end_lineno') and node.end_lineno:
                    lines = node.end_lineno - node.lineno
                    if lines > 50:  # 超过50行的函数
                        self.add_issue(
                            file_path,
                            "函数过长",
                            f"函数 '{node.name}' 第{node.lineno}行: {lines}行，建议拆分"
                        )
    
    def check_security_issues(self, file_path: Path, content: str):
        """检查安全问题"""
        # 跳过检查工具本身，避免误报
        if 'code_quality_checker.py' in str(file_path):
            return

        security_patterns = [
            ('eval(', '使用eval()函数存在安全风险'),
            ('exec(', '使用exec()函数存在安全风险'),
            ('os.system(', '使用os.system()存在命令注入风险'),
            ('subprocess.call(', '使用subprocess.call()需要注意命令注入'),
        ]
        
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            for pattern, message in security_patterns:
                if pattern in line and not line.strip().startswith('#'):
                    # 排除函数定义和注释
                    if 'def ' in line or '"""' in line or "'''" in line:
                        continue
                    self.add_issue(file_path, "安全风险", f"第{i}行: {message}")
    
    def add_issue(self, file_path: Path, issue_type: str, description: str):
        """添加问题"""
        self.issues.append({
            'file': str(file_path.relative_to(self.project_root)),
            'type': issue_type,
            'description': description
        })
    
    def generate_report(self) -> Dict[str, Any]:
        """生成报告"""
        # 按类型分组
        issues_by_type = {}
        for issue in self.issues:
            issue_type = issue['type']
            if issue_type not in issues_by_type:
                issues_by_type[issue_type] = []
            issues_by_type[issue_type].append(issue)
        
        return {
            'total_issues': len(self.issues),
            'issues_by_type': issues_by_type,
            'all_issues': self.issues
        }
    
    def print_report(self, report: Dict[str, Any]):
        """打印报告"""
        print(f"\n📊 代码质量检查报告")
        print("=" * 50)
        
        if report['total_issues'] == 0:
            print("✅ 未发现代码质量问题！")
            return
        
        print(f"⚠️  发现 {report['total_issues']} 个问题")
        print()
        
        for issue_type, issues in report['issues_by_type'].items():
            print(f"🔸 {issue_type} ({len(issues)}个):")
            for issue in issues[:5]:  # 只显示前5个
                print(f"   📁 {issue['file']}")
                print(f"   📝 {issue['description']}")
                print()
            
            if len(issues) > 5:
                print(f"   ... 还有 {len(issues) - 5} 个类似问题")
                print()


def main():
    """主函数"""
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    
    # 创建检查器
    checker = CodeQualityChecker(str(project_root))
    
    # 执行检查
    report = checker.check_all()
    
    # 打印报告
    checker.print_report(report)
    
    # 返回退出码
    if report['total_issues'] > 0:
        print(f"\n💡 建议:")
        print("1. 修复裸露的except语句，指定具体的异常类型")
        print("2. 拆分过长的函数，提高代码可读性")
        print("3. 检查安全风险，避免使用危险函数")
        print("4. 定期运行代码质量检查工具")
        
        return 1
    else:
        print("\n🎉 代码质量良好！")
        return 0


if __name__ == '__main__':
    sys.exit(main())
