#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
警告修复工具
解决启动时的各种警告问题
"""

import os
import sys
import warnings
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def setup_warning_filters():
    """设置警告过滤器"""
    print("🔧 设置警告过滤器...")
    
    # 抑制sklearn版本兼容性警告
    warnings.filterwarnings('ignore', category=UserWarning, module='sklearn')
    warnings.filterwarnings('ignore', message='.*InconsistentVersionWarning.*')
    warnings.filterwarnings('ignore', message='.*version.*when using version.*')
    
    # 抑制pkg_resources弃用警告
    warnings.filterwarnings('ignore', category=DeprecationWarning, module='pkg_resources')
    warnings.filterwarnings('ignore', message='.*pkg_resources is deprecated.*')
    
    # 抑制其他常见警告
    warnings.filterwarnings('ignore', category=FutureWarning, module='sklearn')
    warnings.filterwarnings('ignore', category=DeprecationWarning, module='numpy')
    
    print("✓ 警告过滤器设置完成")

def check_sklearn_compatibility():
    """检查sklearn兼容性"""
    try:
        import sklearn
        print(f"📊 sklearn版本: {sklearn.__version__}")
        
        # 检查模型文件
        model_path = project_root / 'predictive_model' / 'svm_anomaly_detection_model.pkl'
        if model_path.exists():
            print(f"✓ 模型文件存在: {model_path}")
            
            # 尝试加载模型（抑制警告）
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                
                import pickle
                try:
                    with open(model_path, 'rb') as f:
                        model = pickle.load(f)
                    print("✓ 模型加载成功（警告已抑制）")
                    return True
                except Exception as e:
                    print(f"❌ 模型加载失败: {e}")
                    return False
        else:
            print(f"⚠️  模型文件不存在: {model_path}")
            return False
            
    except ImportError:
        print("❌ sklearn未安装")
        return False

def check_pkg_resources():
    """检查pkg_resources使用情况"""
    print("📦 检查pkg_resources使用情况...")
    
    # 检查run.py中的使用
    run_py_path = project_root / 'run.py'
    if run_py_path.exists():
        with open(run_py_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'pkg_resources' in content:
            print("⚠️  run.py中仍使用pkg_resources")
            print("   建议使用importlib.metadata替代")
        else:
            print("✓ run.py已使用现代导入方法")
    
    # 检查其他文件
    for py_file in project_root.rglob('*.py'):
        if py_file.name.startswith('.'):
            continue
            
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if 'import pkg_resources' in content or 'from pkg_resources' in content:
                print(f"⚠️  {py_file.relative_to(project_root)} 使用pkg_resources")
        except (UnicodeDecodeError, OSError):
            # 文件读取失败，跳过
            continue

def create_warning_suppression_module():
    """创建警告抑制模块"""
    print("📝 创建警告抑制模块...")
    
    suppression_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
警告抑制模块
在应用启动时导入此模块以抑制常见警告
"""

import warnings
import os

# 设置环境变量以抑制警告
os.environ['PYTHONWARNINGS'] = 'ignore::UserWarning:sklearn,ignore::DeprecationWarning:pkg_resources'

# 全局警告过滤器
def setup_global_warning_filters():
    """设置全局警告过滤器"""
    # sklearn相关警告
    warnings.filterwarnings('ignore', category=UserWarning, module='sklearn')
    warnings.filterwarnings('ignore', message='.*InconsistentVersionWarning.*')
    warnings.filterwarnings('ignore', message='.*version.*when using version.*')
    
    # pkg_resources弃用警告
    warnings.filterwarnings('ignore', category=DeprecationWarning, module='pkg_resources')
    warnings.filterwarnings('ignore', message='.*pkg_resources is deprecated.*')
    
    # 其他常见警告
    warnings.filterwarnings('ignore', category=FutureWarning, module='sklearn')
    warnings.filterwarnings('ignore', category=DeprecationWarning, module='numpy')

# 自动执行
setup_global_warning_filters()
'''
    
    suppression_file = project_root / 'vibmon_app' / 'utils' / 'warning_suppression.py'
    with open(suppression_file, 'w', encoding='utf-8') as f:
        f.write(suppression_code)
    
    print(f"✓ 警告抑制模块已创建: {suppression_file}")
    return suppression_file

def update_app_init():
    """更新应用初始化文件以导入警告抑制"""
    print("🔄 更新应用初始化文件...")
    
    init_file = project_root / 'vibmon_app' / '__init__.py'
    
    with open(init_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已经导入
    if 'warning_suppression' in content:
        print("✓ 警告抑制已导入")
        return
    
    # 在文件开头添加导入
    lines = content.split('\n')
    
    # 找到合适的位置插入
    insert_index = 0
    for i, line in enumerate(lines):
        if line.startswith('import') or line.startswith('from'):
            insert_index = i
            break
    
    # 插入警告抑制导入
    lines.insert(insert_index, '# 抑制常见警告')
    lines.insert(insert_index + 1, 'from vibmon_app.utils import warning_suppression')
    lines.insert(insert_index + 2, '')
    
    # 写回文件
    with open(init_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(lines))
    
    print("✓ 应用初始化文件已更新")

def test_warning_suppression():
    """测试警告抑制效果"""
    print("🧪 测试警告抑制效果...")
    
    try:
        # 测试sklearn模型加载
        from vibmon_app import create_app
        
        app = create_app()
        with app.app_context():
            from vibmon_app.models.ml_models import model_manager
            
            print("  测试模型加载...")
            success = model_manager.load_svm_model()
            if success:
                print("✓ 模型加载成功，无警告输出")
            else:
                print("⚠️  模型加载失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 警告修复工具")
    print("=" * 50)
    
    # 设置警告过滤器
    setup_warning_filters()
    
    # 检查sklearn兼容性
    sklearn_ok = check_sklearn_compatibility()
    
    # 检查pkg_resources使用
    check_pkg_resources()
    
    # 创建警告抑制模块
    suppression_file = create_warning_suppression_module()
    
    # 更新应用初始化
    update_app_init()
    
    # 测试警告抑制
    test_ok = test_warning_suppression()
    
    print("\n" + "=" * 50)
    print("📋 修复结果:")
    print(f"✓ sklearn兼容性: {'正常' if sklearn_ok else '需要重新训练模型'}")
    print(f"✓ 警告抑制模块: 已创建")
    print(f"✓ 应用初始化: 已更新")
    print(f"✓ 功能测试: {'通过' if test_ok else '失败'}")
    
    if not sklearn_ok:
        print("\n💡 建议:")
        print("   运行 python tools/retrain_svm_model.py 重新训练模型")
    
    print("\n🎉 警告修复完成！")
    print("   现在启动应用应该不会看到警告信息了")

if __name__ == '__main__':
    main()
