# MinIO配置文件
# 用于振动监测系统的MinIO集成
#
# 🔧 环境变量覆盖说明：
# Docker部署时，以下环境变量会覆盖此配置文件的设置：
# - MINIO_ENDPOINT: MinIO服务器地址
# - MINIO_ACCESS_KEY: MinIO访问密钥
# - MINIO_SECRET_KEY: MinIO密钥
# - MINIO_SECURE: 是否使用HTTPS (true/false)
# - MINIO_REGION: MinIO区域
# - MINIO_INPUT_BUCKET: 输入数据存储桶
# - MINIO_OUTPUT_BUCKET: 输出结果存储桶

# MinIO服务器连接配置 (可被环境变量覆盖)
minio:
  endpoint: "http://47.122.157.161:9000/"  # 可被MINIO_ENDPOINT覆盖
  access_key: "15871591512"                 # 可被MINIO_ACCESS_KEY覆盖
  secret_key: "15871591512yn"               # 可被MINIO_SECRET_KEY覆盖
  secure: true                             # 可被MINIO_SECURE覆盖
  region: "us-east-1"                       # 可被MINIO_REGION覆盖

# 存储桶配置 (可被环境变量覆盖)
buckets:
  input_bucket: "test1"                     # 可被MINIO_INPUT_BUCKET覆盖
  output_bucket: "test2"                    # 可被MINIO_OUTPUT_BUCKET覆盖
  allow_create_bucket: true

# 文件路径配置
paths:
  timezone: "Asia/Shanghai"
  timestamp_format: "unix_ms"
  path_pattern: "{bucket}/{year}/{month}/{date}/{hour}/{device_id}/{timestamp}.csv"

# CSV格式配置
csv_format:
  encoding: "utf-8"
  separator: ","
  decimal_places:
    time: 6
    frequency: 3
    amplitude: 6
    bearing: 2
  null_value: ""
  include_header: true

# 性能配置
processing:
  max_file_size_mb: 500
  timeout_seconds: 600
  chunk_size: 8192
  memory_limit_mb: 1024

# 错误处理配置
error_handling:
  retry_attempts: 3
  retry_delay_seconds: 1
  log_level: "INFO"
