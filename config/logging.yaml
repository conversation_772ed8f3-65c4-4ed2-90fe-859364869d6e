# 振动监测系统日志配置
# 根据不同的使用场景优化日志策略

# 日志级别配置
log_levels:
  # 最小日志级别 - 适用于高频调用场景
  minimal:
    description: "只记录API请求/响应和错误，适用于每5秒调用的生产环境"
    includes:
      - API请求开始/结束
      - 错误信息
      - 统计信息
    excludes:
      - 详细参数
      - 处理步骤
      - 结果详情
    estimated_size_per_call: "0.5KB"
    
  # 标准日志级别 - 平衡性能和可观测性
  standard:
    description: "记录关键处理步骤，适用于正常监控"
    includes:
      - API请求开始/结束
      - 关键处理步骤
      - 简化的结果摘要
      - 错误信息
    excludes:
      - 详细参数
      - 详细处理信息
    estimated_size_per_call: "1KB"
    
  # 详细日志级别 - 适用于调试和开发
  detailed:
    description: "记录所有详细信息，适用于开发和故障排查"
    includes:
      - 所有信息
    estimated_size_per_call: "2KB"

# 文件轮转配置
rotation:
  main_log:
    max_size: "5MB"
    backup_count: 20
    total_capacity: "100MB"
    
  error_log:
    max_size: "2MB"
    backup_count: 10
    total_capacity: "20MB"
    
  api_stats:
    max_size: "1MB"
    backup_count: 30
    total_capacity: "30MB"

# 清理策略
cleanup:
  compress_after_days: 7
  delete_after_days: 30
  
# 性能优化
performance:
  # 异步日志写入
  async_logging: true
  
  # 批量写入
  batch_size: 100
  batch_timeout: 5  # 秒
  
  # 缓冲区大小
  buffer_size: 8192  # 字节

# 监控阈值
monitoring:
  # 日志文件大小告警阈值
  size_warning_threshold: "50MB"
  size_critical_threshold: "100MB"
  
  # API调用频率告警
  call_rate_warning: 15  # 每分钟超过15次
  call_rate_critical: 20  # 每分钟超过20次
  
  # 错误率告警
  error_rate_warning: 0.05  # 5%
  error_rate_critical: 0.10  # 10%

# 使用场景建议
scenarios:
  high_frequency:
    description: "高频调用场景（每5秒一次）"
    recommended_level: "minimal"
    settings:
      log_level: "MINIMAL"
      rotation_size: "2MB"
      backup_count: 50
      compress_after_days: 3
      delete_after_days: 14
    
  normal_monitoring:
    description: "正常监控场景"
    recommended_level: "standard"
    settings:
      log_level: "STANDARD"
      rotation_size: "5MB"
      backup_count: 20
      compress_after_days: 7
      delete_after_days: 30
      
  development:
    description: "开发调试场景"
    recommended_level: "detailed"
    settings:
      log_level: "DETAILED"
      rotation_size: "10MB"
      backup_count: 10
      compress_after_days: 1
      delete_after_days: 7
