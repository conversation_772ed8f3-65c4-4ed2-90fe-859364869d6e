# 振动监测系统 API

**版本**: v2.2.0 Final | **Python**: 3.10.16 | **更新日期**: 2025-07-31

一个基于Flask的高性能振动监测系统API，提供频率幅值计算、异常检测和智能维护建议功能。

## 🚀 快速启动

### 一键启动（推荐）

```bash
# 生产环境 - 自动选择最佳服务器
python run.py --production

# 开发环境 - Flask服务器，标准日志
python run.py --development

# 高性能模式 - 启用所有性能优化
python run.py --production --performance

# 静默模式 - 最少日志输出
python run.py --silent
```

### 环境要求

- **Python**: 3.10.16
- **操作系统**: Windows/Linux/macOS
- **内存**: 建议 2GB+

### 安装依赖

```bash
pip install -r requirements.txt
```

## ✨ 功能特性

### 核心分析功能
- 🔍 **频率幅值计算**: 基于FFT算法的振动信号频域分析
- 📊 **多特征提取**: 时域、频域、统计特征的综合分析
- 🤖 **异常检测**: 使用SVM机器学习模型进行振动异常检测
- ⚙️ **轴承分析**: 轴承特征频率计算和故障诊断
- 💊 **健康度评估**: 设备健康状态实时评估和预警
- 🔧 **智能维护建议**: 基于健康度的科学维护时间预测

### 数据存储与处理
- 🗄️ **MinIO集成**: 支持MinIO对象存储的文件读取和保存
- 🌐 **多格式URL**: 支持`minio://`和`http://`格式的文件URL
- 📋 **CSV格式化**: 列式CSV输出格式，便于数据分析
- 📊 **数据处理**: 支持CSV格式的振动数据文件处理

### 系统性能
- 🚀 **高性能**: 优化的数据处理算法，支持高频调用（每5秒）
- 🔧 **智能服务器选择**: Windows自动使用Waitress，Linux自动使用Gunicorn
- 📝 **优化日志**: 四个日志级别，适应不同场景需求
- ⚡ **性能优化**: 数据缓存、并行处理、数据压缩等优化

## 🔧 服务器配置

| 操作系统 | 自动选择服务器 | 并发能力 | 适用场景 |
|----------|----------------|----------|----------|
| **Windows** | Waitress | 16线程 | 生产环境 |
| **Linux/macOS** | Gunicorn | 8进程 | 生产环境 |
| **开发调试** | Flask | 单线程 | 开发环境 |

## 📊 日志级别

| 级别 | 日志量 | 适用场景 | 启动命令 |
|------|--------|----------|----------|
| **silent** | 几乎无 | 极简生产 | `--silent` |
| **minimal** | 最少 | 生产环境 | `--production` |
| **standard** | 适中 | 开发环境 | `--development` |
| **detailed** | 详细 | 调试监控 | `--monitoring` |

## 🌐 API 接口

### POST /api/features/freq_am

计算振动信号的频率和幅值分析，支持轴承特征频率计算和MinIO存储。

**请求参数:**
```json
{
    "equipment_code": "CAAQ000000497",
    "minio_url": "http://**************:9000/test1/data/input.csv",
    "rpm": 1800
}
```

**响应示例:**
```json
{
    "status": "success",
    "timestamp": "2025-07-30T15:52:52.060756",
    "result": {
        "equipment_code": "CAAQ000000497",
        "minio_output": {
            "minio_url": "http://**************:9000/test2/2025/07/30/15/CAAQ000000497/1753852117976.csv",
            "file_size_bytes": 327677
        }
    }
}
```

### POST /api/features/multi_features

提取多维度特征，包括时域、频域、统计特征等。

**请求参数:**
```json
{
    "equipment_code": "CAAQ000000497",
    "minio_url": "http://**************:9000/test1/data/input.csv",
    "health_degree_flag": 1,
    "predict_flag": 1,
    "early_warning_status": 0,
    "alarm_status": 0,
    "mtce_period": 365
}
```

**响应示例:**
```json
{
    "status": "success",
    "timestamp": "2025-07-30T19:36:23.358744",
    "result": {
        "equipment_code": "CAAQ000000497",
        "rms_values": {
            "horizontal": 0.245,
            "axial": 0.189,
            "vertical": 0.167
        },
        "peak_to_peak": {
            "horizontal": 1.234,
            "axial": 0.987,
            "vertical": 0.876
        },
        "predict_result": "normal",
        "health_degree": 85.6,
        "mtce_advice": 204
    }
}
```

### GET /api/status

系统状态检查接口，返回系统健康状态和性能指标。

**响应示例:**
```json
{
    "status": "healthy",
    "timestamp": "2025-07-30T15:52:52Z",
    "system_info": {
        "cpu_usage": 15.2,
        "memory_usage": 45.8,
        "disk_usage": 23.1
    }
}
```

### 其他接口

- **GET /api/devices**: 获取支持的设备列表
- **POST /api/config/reload**: 重新加载配置文件
- **GET /api/config/info**: 获取配置信息
- **GET /api/performance/stats**: 获取性能统计

## 📁 项目结构

```
vibmon/
├── run.py                    # 统一启动脚本
├── vibmon_app/              # 主应用包
│   ├── __init__.py          # 应用工厂
│   ├── api/                 # API路由
│   │   └── routes.py        # 路由定义
│   ├── models/              # 机器学习模型
│   │   └── ml_models.py     # SVM模型管理
│   ├── services/            # 业务逻辑
│   │   └── data_processing.py # 数据处理服务
│   └── utils/               # 工具函数
│       ├── logging_utils.py # 日志工具
│       └── warning_suppression.py # 警告抑制
├── csv_data/                # 振动数据文件
├── logs/                    # 日志文件
├── predictive_model/        # 预训练SVM模型
├── tools/                   # 工具脚本
│   ├── fix_warnings.py      # 警告修复工具
│   └── retrain_svm_model.py # 模型重训练工具
└── requirements.txt         # 依赖包列表
```

## 🚀 部署指南

### Docker 部署

```bash
# 构建镜像
docker build -t vibmon-api .

# 运行容器
docker run -p 5000:5000 vibmon-api
```

### 生产环境部署

```bash
# Windows生产环境（推荐）
python run.py --production --performance

# Linux生产环境（推荐）
python run.py --production --server auto

# 自定义配置
python run.py --mode production --log-level minimal --workers 16
```

## 🔧 高级配置

### 性能优化

```bash
# 启用所有性能优化
python run.py --production --performance
```

自动启用：
- 数据压缩（4000个数据点）
- 优化的CSV读取
- 向量化数学运算

### 自定义配置

```bash
# 自定义服务器和端口
python run.py --production --host 0.0.0.0 --port 8080

# 自定义工作进程数
python run.py --production --workers 16 --threads 32

# 查看所有配置选项
python run.py --info
```

## 🧪 测试

### 运行测试

```bash
python -m pytest tests/ -v
```

### 健康检查

```bash
curl http://localhost:5000/health
```

### API测试

```bash
curl -X POST http://localhost:5000/api/freq_am \
  -H "Content-Type: application/json" \
  -d '{"equipment_code": "0001"}'
```

## 🛠️ 故障排查

### 常见问题

1. **启动失败**：检查Python版本和依赖包
2. **模型加载失败**：运行 `python tools/retrain_svm_model.py`
3. **警告信息**：运行 `python tools/fix_warnings.py`
4. **性能问题**：使用 `--performance` 参数

### 日志查看

```bash
# 查看应用日志
tail -f logs/vibmon.log

# 查看错误日志
tail -f logs/vibmon_error.log

# 查看API统计
tail -f logs/api_stats.log
```

## 📋 版本信息

- **项目版本**: 2.2.0 Final
- **Python版本**: 3.10.16
- **最后更新**: 2025-07-31

### 🆕 v2.2.0 更新内容
- **智能维护建议**: 新增基于健康度的维护时间预测功能
- **配置优化**: 完善环境变量覆盖机制，简化Docker部署
- **Bug修复**: 修复存储桶配置和资源检测相关问题

## 📄 许可证

MIT License
